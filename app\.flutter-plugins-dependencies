{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "connectivity_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\connectivity_plus-6.1.4\\\\", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\device_info_plus-11.3.0\\\\", "native_build": true, "dependencies": []}, {"name": "file_picker", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\file_picker-10.2.0\\\\", "native_build": true, "dependencies": []}, {"name": "file_selector_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\file_selector_ios-0.5.3+1\\\\", "native_build": true, "dependencies": []}, {"name": "gal", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\gal-2.3.2\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "image_picker_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\image_picker_ios-0.8.12+2\\\\", "native_build": true, "dependencies": []}, {"name": "network_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\network_info_plus-6.1.4\\\\", "native_build": true, "dependencies": []}, {"name": "open_filex", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\open_filex-4.7.0\\\\", "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\package_info_plus-8.3.0\\\\", "native_build": true, "dependencies": []}, {"name": "pasteboard", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\pasteboard-0.2.0\\\\", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\path_provider_foundation-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "permission_handler_apple", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\permission_handler_apple-9.1.4\\\\", "native_build": true, "dependencies": []}, {"name": "photo_manager", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\photo_manager-3.7.1\\\\", "native_build": true, "dependencies": []}, {"name": "share_handler_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\share_handler_ios-0.0.15\\\\", "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\shared_preferences_foundation-2.5.4\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "system_settings", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\system_settings-2.1.0\\\\", "native_build": true, "dependencies": []}, {"name": "url_launcher_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\url_launcher_ios-6.3.3\\\\", "native_build": true, "dependencies": []}, {"name": "video_player_avfoundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\video_player_avfoundation-2.7.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\wakelock_plus-1.3.2\\\\", "native_build": true, "dependencies": ["package_info_plus"]}], "android": [{"name": "connectivity_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\connectivity_plus-6.1.4\\\\", "native_build": true, "dependencies": []}, {"name": "desktop_drop", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\desktop_drop-0.6.1\\\\", "native_build": true, "dependencies": []}, {"name": "device_apps", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\device_apps-2.2.0\\\\", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\device_info_plus-11.3.0\\\\", "native_build": true, "dependencies": []}, {"name": "dynamic_color", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\dynamic_color-1.7.0\\\\", "native_build": true, "dependencies": []}, {"name": "file_picker", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\file_picker-10.2.0\\\\", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "file_selector_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\file_selector_android-0.5.1+12\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_displaymode", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\flutter_displaymode-0.6.0\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\flutter_plugin_android_lifecycle-2.0.26\\\\", "native_build": true, "dependencies": []}, {"name": "gal", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\gal-2.3.2\\\\", "native_build": true, "dependencies": []}, {"name": "image_picker_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\image_picker_android-0.8.12+21\\\\", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "network_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\network_info_plus-6.1.4\\\\", "native_build": true, "dependencies": []}, {"name": "open_filex", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\open_filex-4.7.0\\\\", "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\package_info_plus-8.3.0\\\\", "native_build": true, "dependencies": []}, {"name": "path_provider_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\path_provider_android-2.2.15\\\\", "native_build": true, "dependencies": []}, {"name": "permission_handler_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\permission_handler_android-11.1.0\\\\", "native_build": true, "dependencies": []}, {"name": "photo_manager", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\photo_manager-3.7.1\\\\", "native_build": true, "dependencies": []}, {"name": "share_handler_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\share_handler_android-0.0.10\\\\", "native_build": true, "dependencies": []}, {"name": "shared_preferences_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\shared_preferences_android-2.4.7\\\\", "native_build": true, "dependencies": []}, {"name": "shared_storage", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\shared_storage-0.8.1\\\\", "native_build": true, "dependencies": []}, {"name": "system_settings", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\system_settings-2.1.0\\\\", "native_build": true, "dependencies": []}, {"name": "url_launcher_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\url_launcher_android-6.3.14\\\\", "native_build": true, "dependencies": []}, {"name": "video_player_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\video_player_android-2.7.16\\\\", "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\wakelock_plus-1.3.2\\\\", "native_build": true, "dependencies": ["package_info_plus"]}], "macos": [{"name": "connectivity_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\connectivity_plus-6.1.4\\\\", "native_build": true, "dependencies": []}, {"name": "desktop_drop", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\desktop_drop-0.6.1\\\\", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\device_info_plus-11.3.0\\\\", "native_build": true, "dependencies": []}, {"name": "dynamic_color", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\dynamic_color-1.7.0\\\\", "native_build": true, "dependencies": []}, {"name": "file_picker", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\file_picker-10.2.0\\\\", "native_build": true, "dependencies": []}, {"name": "file_selector_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\file_selector_macos-0.9.4+2\\\\", "native_build": true, "dependencies": []}, {"name": "gal", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\gal-2.3.2\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "image_picker_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\image_picker_macos-0.2.1+2\\\\", "native_build": false, "dependencies": ["file_selector_macos"]}, {"name": "network_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\network_info_plus-6.1.4\\\\", "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\package_info_plus-8.3.0\\\\", "native_build": true, "dependencies": []}, {"name": "pasteboard", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\pasteboard-0.2.0\\\\", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\path_provider_foundation-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "photo_manager", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\photo_manager-3.7.1\\\\", "native_build": true, "dependencies": []}, {"name": "screen_retriever_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\screen_retriever_macos-0.2.0\\\\", "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\shared_preferences_foundation-2.5.4\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "system_tray", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\system_tray-2.0.3\\\\", "native_build": true, "dependencies": []}, {"name": "tray_manager", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\git\\\\tray_manager-b37f5e088e0f02c45a684ae41e9d2da2d5c596db\\\\", "native_build": true, "dependencies": []}, {"name": "url_launcher_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\url_launcher_macos-3.2.2\\\\", "native_build": true, "dependencies": []}, {"name": "video_player_avfoundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\video_player_avfoundation-2.7.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\wakelock_plus-1.3.2\\\\", "native_build": true, "dependencies": ["package_info_plus"]}, {"name": "window_manager", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\window_manager-0.5.1\\\\", "native_build": true, "dependencies": []}], "linux": [{"name": "connectivity_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\connectivity_plus-6.1.4\\\\", "native_build": false, "dependencies": []}, {"name": "desktop_drop", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\desktop_drop-0.6.1\\\\", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\device_info_plus-11.3.0\\\\", "native_build": false, "dependencies": []}, {"name": "dynamic_color", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\dynamic_color-1.7.0\\\\", "native_build": true, "dependencies": []}, {"name": "file_picker", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\file_picker-10.2.0\\\\", "native_build": false, "dependencies": []}, {"name": "file_selector_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\file_selector_linux-0.9.3+2\\\\", "native_build": true, "dependencies": []}, {"name": "image_picker_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\image_picker_linux-0.2.1+2\\\\", "native_build": false, "dependencies": ["file_selector_linux"]}, {"name": "network_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\network_info_plus-6.1.4\\\\", "native_build": false, "dependencies": []}, {"name": "package_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\package_info_plus-8.3.0\\\\", "native_build": false, "dependencies": []}, {"name": "pasteboard", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\pasteboard-0.2.0\\\\", "native_build": true, "dependencies": []}, {"name": "path_provider_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\path_provider_linux-2.2.1\\\\", "native_build": false, "dependencies": []}, {"name": "screen_retriever_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\screen_retriever_linux-0.2.0\\\\", "native_build": true, "dependencies": []}, {"name": "shared_preferences_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\shared_preferences_linux-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_linux"]}, {"name": "system_tray", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\system_tray-2.0.3\\\\", "native_build": true, "dependencies": []}, {"name": "url_launcher_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\url_launcher_linux-3.2.1\\\\", "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\wakelock_plus-1.3.2\\\\", "native_build": false, "dependencies": ["package_info_plus"]}, {"name": "window_manager", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\window_manager-0.5.1\\\\", "native_build": true, "dependencies": []}], "windows": [{"name": "connectivity_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\connectivity_plus-6.1.4\\\\", "native_build": true, "dependencies": []}, {"name": "desktop_drop", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\desktop_drop-0.6.1\\\\", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\device_info_plus-11.3.0\\\\", "native_build": false, "dependencies": []}, {"name": "dynamic_color", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\dynamic_color-1.7.0\\\\", "native_build": true, "dependencies": []}, {"name": "file_picker", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\file_picker-10.2.0\\\\", "native_build": false, "dependencies": []}, {"name": "file_selector_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\file_selector_windows-0.9.3+4\\\\", "native_build": true, "dependencies": []}, {"name": "gal", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\gal-2.3.2\\\\", "native_build": true, "dependencies": []}, {"name": "image_picker_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\image_picker_windows-0.2.1+1\\\\", "native_build": false, "dependencies": ["file_selector_windows"]}, {"name": "network_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\network_info_plus-6.1.4\\\\", "native_build": false, "dependencies": []}, {"name": "package_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\package_info_plus-8.3.0\\\\", "native_build": false, "dependencies": []}, {"name": "pasteboard", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\pasteboard-0.2.0\\\\", "native_build": true, "dependencies": []}, {"name": "path_provider_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\path_provider_windows-2.3.0\\\\", "native_build": false, "dependencies": []}, {"name": "permission_handler_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\git\\\\permission_handler_windows_noop-fc09b707ab4535a9214c87b16f09feda7e765d90\\\\", "native_build": true, "dependencies": []}, {"name": "screen_retriever_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\screen_retriever_windows-0.2.0\\\\", "native_build": true, "dependencies": []}, {"name": "shared_preferences_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\shared_preferences_windows-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_windows"]}, {"name": "system_tray", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\system_tray-2.0.3\\\\", "native_build": true, "dependencies": []}, {"name": "tray_manager", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\git\\\\tray_manager-b37f5e088e0f02c45a684ae41e9d2da2d5c596db\\\\", "native_build": true, "dependencies": []}, {"name": "url_launcher_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\url_launcher_windows-3.1.4\\\\", "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\wakelock_plus-1.3.2\\\\", "native_build": false, "dependencies": ["package_info_plus"]}, {"name": "window_manager", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\window_manager-0.5.1\\\\", "native_build": true, "dependencies": []}], "web": [{"name": "connectivity_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\connectivity_plus-6.1.4\\\\", "dependencies": []}, {"name": "desktop_drop", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\desktop_drop-0.6.1\\\\", "dependencies": []}, {"name": "device_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\device_info_plus-11.3.0\\\\", "dependencies": []}, {"name": "file_picker", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\file_picker-10.2.0\\\\", "dependencies": []}, {"name": "file_selector_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\file_selector_web-0.9.4+2\\\\", "dependencies": []}, {"name": "image_picker_for_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\image_picker_for_web-3.0.6\\\\", "dependencies": []}, {"name": "network_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\network_info_plus-6.1.4\\\\", "dependencies": []}, {"name": "package_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\package_info_plus-8.3.0\\\\", "dependencies": []}, {"name": "shared_preferences_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\shared_preferences_web-2.4.3\\\\", "dependencies": []}, {"name": "url_launcher_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\url_launcher_web-2.3.3\\\\", "dependencies": []}, {"name": "video_player_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\video_player_web-2.3.5\\\\", "dependencies": []}, {"name": "wakelock_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.flutter-io.cn\\\\wakelock_plus-1.3.2\\\\", "dependencies": ["package_info_plus"]}]}, "dependencyGraph": [{"name": "connectivity_plus", "dependencies": []}, {"name": "desktop_drop", "dependencies": []}, {"name": "device_apps", "dependencies": []}, {"name": "device_info_plus", "dependencies": []}, {"name": "dynamic_color", "dependencies": []}, {"name": "file_picker", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "file_selector", "dependencies": ["file_selector_android", "file_selector_ios", "file_selector_linux", "file_selector_macos", "file_selector_web", "file_selector_windows"]}, {"name": "file_selector_android", "dependencies": []}, {"name": "file_selector_ios", "dependencies": []}, {"name": "file_selector_linux", "dependencies": []}, {"name": "file_selector_macos", "dependencies": []}, {"name": "file_selector_web", "dependencies": []}, {"name": "file_selector_windows", "dependencies": []}, {"name": "flutter_displaymode", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "gal", "dependencies": []}, {"name": "image_picker", "dependencies": ["image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_windows"]}, {"name": "image_picker_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "image_picker_for_web", "dependencies": []}, {"name": "image_picker_ios", "dependencies": []}, {"name": "image_picker_linux", "dependencies": ["file_selector_linux"]}, {"name": "image_picker_macos", "dependencies": ["file_selector_macos"]}, {"name": "image_picker_windows", "dependencies": ["file_selector_windows"]}, {"name": "network_info_plus", "dependencies": []}, {"name": "open_filex", "dependencies": []}, {"name": "package_info_plus", "dependencies": []}, {"name": "pasteboard", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "photo_manager", "dependencies": []}, {"name": "screen_retriever", "dependencies": ["screen_retriever_linux", "screen_retriever_macos", "screen_retriever_windows"]}, {"name": "screen_retriever_linux", "dependencies": []}, {"name": "screen_retriever_macos", "dependencies": []}, {"name": "screen_retriever_windows", "dependencies": []}, {"name": "share_handler", "dependencies": ["share_handler_android", "share_handler_ios"]}, {"name": "share_handler_android", "dependencies": []}, {"name": "share_handler_ios", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "shared_storage", "dependencies": []}, {"name": "system_settings", "dependencies": []}, {"name": "system_tray", "dependencies": []}, {"name": "tray_manager", "dependencies": []}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}, {"name": "video_player", "dependencies": ["video_player_android", "video_player_avfoundation", "video_player_web"]}, {"name": "video_player_android", "dependencies": []}, {"name": "video_player_avfoundation", "dependencies": []}, {"name": "video_player_web", "dependencies": []}, {"name": "wakelock_plus", "dependencies": ["package_info_plus"]}, {"name": "window_manager", "dependencies": ["screen_retriever"]}], "date_created": "2025-07-24 11:48:34.948729", "version": "3.24.5", "swift_package_manager_enabled": false}