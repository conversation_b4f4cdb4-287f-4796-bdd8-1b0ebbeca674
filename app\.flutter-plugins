# This is a generated file; do not edit or check into version control.
connectivity_plus=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\connectivity_plus-6.1.4\\
desktop_drop=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\desktop_drop-0.6.1\\
device_apps=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\device_apps-2.2.0\\
device_info_plus=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\device_info_plus-11.3.0\\
dynamic_color=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\dynamic_color-1.7.0\\
file_picker=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\file_picker-10.2.0\\
file_selector=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\file_selector-1.0.3\\
file_selector_android=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\file_selector_android-0.5.1+12\\
file_selector_ios=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\file_selector_ios-0.5.3+1\\
file_selector_linux=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\file_selector_linux-0.9.3+2\\
file_selector_macos=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\file_selector_macos-0.9.4+2\\
file_selector_web=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\file_selector_web-0.9.4+2\\
file_selector_windows=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\file_selector_windows-0.9.3+4\\
flutter_displaymode=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\flutter_displaymode-0.6.0\\
flutter_plugin_android_lifecycle=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\flutter_plugin_android_lifecycle-2.0.26\\
gal=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\gal-2.3.2\\
image_picker=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\image_picker-1.1.2\\
image_picker_android=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\image_picker_android-0.8.12+21\\
image_picker_for_web=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\image_picker_for_web-3.0.6\\
image_picker_ios=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\image_picker_ios-0.8.12+2\\
image_picker_linux=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\image_picker_linux-0.2.1+2\\
image_picker_macos=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\image_picker_macos-0.2.1+2\\
image_picker_windows=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\image_picker_windows-0.2.1+1\\
network_info_plus=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\network_info_plus-6.1.4\\
open_filex=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\open_filex-4.7.0\\
package_info_plus=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\package_info_plus-8.3.0\\
pasteboard=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\pasteboard-0.2.0\\
path_provider=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider-2.1.5\\
path_provider_android=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_android-2.2.15\\
path_provider_foundation=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_foundation-2.4.1\\
path_provider_linux=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_linux-2.2.1\\
path_provider_windows=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_windows-2.3.0\\
permission_handler=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\permission_handler-11.0.1\\
permission_handler_android=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\permission_handler_android-11.1.0\\
permission_handler_apple=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\permission_handler_apple-9.1.4\\
permission_handler_windows=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\git\\permission_handler_windows_noop-fc09b707ab4535a9214c87b16f09feda7e765d90\\
photo_manager=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\photo_manager-3.7.1\\
screen_retriever=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\screen_retriever-0.2.0\\
screen_retriever_linux=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\screen_retriever_linux-0.2.0\\
screen_retriever_macos=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\screen_retriever_macos-0.2.0\\
screen_retriever_windows=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\screen_retriever_windows-0.2.0\\
share_handler=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\share_handler-0.0.23\\
share_handler_android=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\share_handler_android-0.0.10\\
share_handler_ios=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\share_handler_ios-0.0.15\\
shared_preferences=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\shared_preferences-2.5.3\\
shared_preferences_android=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\shared_preferences_android-2.4.7\\
shared_preferences_foundation=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\shared_preferences_foundation-2.5.4\\
shared_preferences_linux=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\shared_preferences_linux-2.4.1\\
shared_preferences_web=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\shared_preferences_web-2.4.3\\
shared_preferences_windows=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\shared_preferences_windows-2.4.1\\
shared_storage=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\shared_storage-0.8.1\\
system_settings=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\system_settings-2.1.0\\
system_tray=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\system_tray-2.0.3\\
tray_manager=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\git\\tray_manager-b37f5e088e0f02c45a684ae41e9d2da2d5c596db\\
url_launcher=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\url_launcher-6.3.1\\
url_launcher_android=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\url_launcher_android-6.3.14\\
url_launcher_ios=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\url_launcher_ios-6.3.3\\
url_launcher_linux=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\url_launcher_linux-3.2.1\\
url_launcher_macos=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\url_launcher_macos-3.2.2\\
url_launcher_web=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\url_launcher_web-2.3.3\\
url_launcher_windows=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\url_launcher_windows-3.1.4\\
video_player=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\video_player-2.9.5\\
video_player_android=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\video_player_android-2.7.16\\
video_player_avfoundation=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\video_player_avfoundation-2.7.1\\
video_player_web=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\video_player_web-2.3.5\\
wakelock_plus=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\wakelock_plus-1.3.2\\
window_manager=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\window_manager-0.5.1\\
