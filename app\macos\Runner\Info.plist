<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
    <string>LocalSend</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIconFile</key>
	<string></string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSMinimumSystemVersion</key>
	<string>$(MACOSX_DEPLOYMENT_TARGET)</string>
	<key>NSHumanReadableCopyright</key>
	<string>$(PRODUCT_COPYRIGHT)</string>
	<key>NSMainNibFile</key>
	<string>MainMenu</string>
	<key>NSPrincipalClass</key>
	<string>NSApplication</string>
    <key>LSApplicationCategoryType</key>
    <string>public.app-category.utilities</string>

    <!-- Flag to skip question by Appstore -->
    <key>ITSAppUsesNonExemptEncryption</key>
    <false/>

	<key>CFBundleLocalizations</key>
    <array>
        <string>ar</string>
        <string>bn</string>
        <string>cs</string>
        <string>de</string>
        <string>en</string>
        <string>es-ES</string>
        <string>eu</string>
        <string>fa</string>
        <string>fr-FR</string>
        <string>he</string>
        <string>hu</string>
        <string>id</string>
        <string>it</string>
        <string>ja</string>
        <string>ko</string>
        <string>ne</string>
        <string>nl</string>
        <string>pl</string>
        <string>pt-BR</string>
        <string>ru</string>
        <string>sv</string>
        <string>th</string>
        <string>tr</string>
        <string>uk</string>
        <string>vi</string>
        <string>zh-CN</string>
        <string>zh-HK</string>
        <string>zh-TW</string>
    </array>

	<key>NSPhotoLibraryUsageDescription</key>
    <string>The app needs photo library access so that the user can select photos to share.</string>
    <key>NSPhotoLibraryAddUsageDescription</key>
    <string>The app saves received media to the photo library.</string>
    <key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
    <string>The app requires the local IP to communicate.</string>
    <key>NSLocationWhenInUseUsageDescription</key>
    <string>The app requires the local IP to communicate.</string>
    <key>NSLocalNetworkUsageDescription</key>
    <string>The app uses the local network to find and connect to nearby devices.</string>
    <key>NSBonjourServices</key>
    <array>
        <string>_http._tcp</string>
        <string>_bonjour._tcp</string>
        <string>_lnp._tcp.</string>
    </array>
</dict>
</plist>
