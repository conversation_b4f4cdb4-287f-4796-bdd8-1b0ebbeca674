{"locale": "De<PERSON>ch", "appName": "LocalSend", "general": {"accept": "Akzeptieren", "accepted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "add": "Hinzufügen", "advanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancel": "Abbrechen", "close": "Schließen", "confirm": "Bestätigen", "continueStr": "<PERSON><PERSON>", "copy": "<PERSON><PERSON><PERSON>", "copiedToClipboard": "In die Zwischenablage kopiert", "decline": "<PERSON><PERSON><PERSON><PERSON>", "done": "<PERSON><PERSON><PERSON>", "delete": "Löschen", "edit": "<PERSON><PERSON><PERSON>", "error": "<PERSON><PERSON>", "example": "Beispiel", "files": "<PERSON><PERSON>", "finished": "Abgeschlossen", "hide": "Verstecken", "off": "Aus", "offline": "Offline", "on": "An", "online": "Online", "open": "<PERSON><PERSON><PERSON>", "queue": "Warteschlange", "quickSave": "Quick Save", "renamed": "Umbenannt", "reset": "Z<PERSON>ücksetzen", "restart": "Neustarten", "settings": "Einstellungen", "skipped": "Übersprungen", "start": "Start", "stop": "Stop", "save": "Speichern", "unchanged": "Unverändert", "unknown": "Unbekannt", "noItemInClipboard": "<PERSON><PERSON> Element in der Zwischenablage gefunden."}, "receiveTab": {"title": "Empfangen", "infoBox": {"ip": "IP:", "port": "Port:", "alias": "Alias:"}}, "sendTab": {"title": "Senden", "selection": {"title": "Auswahl", "files": "Dateien: {files}", "size": "Größe: {size}"}, "picker": {"file": "<PERSON><PERSON>", "folder": "<PERSON><PERSON><PERSON>", "media": "Medien", "text": "Text", "app": "App", "clipboard": "Ablage"}, "shareIntentInfo": "Du kannst auch die \"Teilen\"-Funktion deines mobilen Geräts nutzen, um Dateien einfacher auszuwählen.", "nearbyDevices": "Geräte in der Nähe", "thisDevice": "<PERSON><PERSON>", "scan": "<PERSON><PERSON><PERSON><PERSON>en", "sendMode": "<PERSON><PERSON><PERSON>", "sendModes": {"single": "Einzelner Empfänger", "multiple": "Mehrere Empfänger", "link": "<PERSON> <PERSON> te<PERSON>"}, "sendModeHelp": "Erklärung", "help": "<PERSON>te stelle sicher, dass sich das gewünschte Ziel auch im selben WLAN-Netzwerk befindet.", "placeItems": "<PERSON><PERSON>, um zu teilen."}, "settingsTab": {"title": "Einstellungen", "general": {"title": "Allgemein", "brightness": "Helligkeit", "brightnessOptions": {"system": "System", "dark": "<PERSON><PERSON><PERSON>", "light": "Hell"}, "color": "Farbe", "colorOptions": {"system": "System", "oled": "OLED"}, "language": "<PERSON><PERSON><PERSON>", "languageOptions": {"system": "System"}, "saveWindowPlacement": "Schließen: Fensterposition speichern", "minimizeToTray": "Schließen: in Symbolleiste minimieren", "launchAtStartup": "Autostart nach Login", "launchMinimized": "Autostart: versteckt starten", "animations": "<PERSON><PERSON>"}, "receive": {"title": "Empfangen", "quickSave": "@:general.quickSave", "destination": "Ziel-Ordner", "downloads": "(Downloads)", "saveToGallery": "Medien in die Gallerie speichern", "saveToHistory": "In Verlauf s<PERSON>ichern"}, "network": {"title": "Netzwerk", "needRestart": "<PERSON>e den Server neu, um die Änderungen zu übernehmen!", "server": "Server", "alias": "<PERSON><PERSON>", "deviceType": "Gerätetyp", "deviceModel": "Ger<PERSON><PERSON>odell", "port": "Port", "portWarning": "Möglicherweise wirst du von anderen Geräten nicht erkannt, weil du einen benutzerdefinierten Port verwendest. (Standard: {defaultPort})", "encryption": "Verschlüsselung", "multicastGroup": "Multicast", "multicastGroupWarning": "Möglicherweise wirst du von anderen Geräten nicht erkannt, weil du eine benutzerdefinierte Multicast-Adresse verwendest. (Standard: {defaultMulticast})"}, "advancedSettings": "Erweiterte Einstellungen"}, "troubleshootPage": {"title": "Fehlerbehebung", "subTitle": "Diese App funktioniert nicht wie erwartet? Hier findest du häufige Lösungsansätze.", "solution": "Lösung:", "fixButton": "Automatisch beheben", "firewall": {"symptom": "<PERSON>se A<PERSON> kann Dateien an andere Geräte senden, aber nicht umgekehrt.", "solution": "Dies ist höchstwahrscheinlich ein Firewall-Problem. Du kannst es lösen, indem du eingehende Verbindungen (UDP und TCP) auf Port {port} zulässt.", "openFirewall": "Firewall öffnen"}, "noConnection": {"symptom": "<PERSON><PERSON> Geräte können sich nicht sehen noch können sie Dateien miteinander teilen.", "solution": "Das Problem tritt auf beiden Seiten auf? <PERSON><PERSON> stel<PERSON> sicher, dass beide Geräte im selben WLAN sind sowie dieselbe Konfiguration (Port, Multicast-Adresse, Verschlüsselung) haben. <PERSON><PERSON> kann sein, dass das WLAN keine Kommunikation zwischen Teilnehmern erlaubt. In diesem Fall muss am Router diese Option aktiviert werden."}}, "receiveHistoryPage": {"title": "<PERSON><PERSON><PERSON><PERSON>", "openFolder": "Ordner öffnen", "deleteHistory": "<PERSON><PERSON><PERSON><PERSON>", "empty": "<PERSON> Verlauf ist leer.", "entryActions": {"open": "<PERSON><PERSON>", "info": "Information", "deleteFromHistory": "V<PERSON> Verlauf <PERSON>"}}, "apkPickerPage": {"title": "Apps (APK)", "excludeSystemApps": "System-Apps ausschließen", "excludeAppsWithoutLaunchIntent": "Nicht-startbare Apps ausschließen", "apps": "{n} Apps"}, "selectedFilesPage": {"deleteAll": "Alle löschen"}, "receivePage": {"subTitle": {"one": "möchte dir eine Datei senden.", "other": "möchte dir {n} <PERSON><PERSON> senden."}, "subTitleMessage": "sendet dir eine <PERSON>:", "subTitleLink": "sendet dir einen <PERSON>:", "canceled": "Der Absender hat die Anfrage abgebrochen."}, "receiveOptionsPage": {"title": "Optionen", "destination": "@:settingsTab.receive.destination", "appDirectory": "(LocalSend-Ordner)", "saveToGallery": "@:settingsTab.receive.saveToGallery", "saveToGalleryOff": "<PERSON><PERSON><PERSON><PERSON><PERSON> von Ordnern automatisch ausgeschaltet."}, "sendPage": {"waiting": "Warte auf Antwort...", "rejected": "Der Empfänger hat die Anfrage abgelehnt.", "busy": "Der Empfänger ist mit einer anderen Anfrage beschäftigt."}, "progressPage": {"titleSending": "Sende <PERSON>", "titleReceiving": "Empfang<PERSON>", "savedToGallery": "in Fotos ges<PERSON>", "total": {"title": {"sending": "Gesamtfortschritt ({time})", "finishedError": "Abgeschlossen mit Fehler", "canceledSender": "Abgebrochen durch Absender", "canceledReceiver": "Abgebrochen durch Empfänger"}, "count": "Dateien: {curr} / {n}", "size": "Größe: {curr} / {n}", "speed": "Geschwindigkeit: {speed}/s"}}, "webSharePage": {"title": "<PERSON> <PERSON> te<PERSON>", "loading": "Starte Server...", "stopping": "Stoppe server...", "error": "Ein Fehler ist beim Starten des Servers aufgetreten.", "openLink": {"one": "<PERSON><PERSON><PERSON> diesen Link im Browser:", "other": "<PERSON><PERSON><PERSON> einer dieser Links im Browser:"}, "requests": "Anfragen", "noRequests": "<PERSON><PERSON>er keine Anfragen erhalten.", "encryption": "@:settingsTab.network.encryption", "encryptionHint": "LocalSend verwendet ein selbstsigniertes Zertifikat. Sie müssen es im Browser akzeptieren.", "pendingRequests": "Offene Anfragen: {n}"}, "aboutPage": {"title": "Über LocalSend"}, "changelogPage": {"title": "Changelog"}, "aliasGenerator(ignoreMissing)": {"@info": "Inherits from the English version"}, "dialogs": {"addFile": {"title": "Zur Auswahl hinzufügen", "content": "Was möchtest du zum Senden hinzufügen?"}, "addressInput": {"title": "<PERSON><PERSON><PERSON> e<PERSON>", "hashtag": "Hashtag", "ip": "IP-Adresse", "recentlyUsed": "Zuletzt verwendet: "}, "cancelSession": {"title": "Dateiübertragung abbrechen", "content": "Möchtest du wirklich die Dateiübertragung abbrechen?"}, "cannotOpenFile": {"title": "Öffnen fehlgeschlagen", "content": "Die Datei \"{file}\" konnte nicht geöffnet werden. <PERSON>rde diese Datei verschoben, umbenannt oder gelöscht?"}, "encryptionDisabledNotice": {"title": "Verschlüsselung deaktiviert", "content": "Die Kommunikation erfolgt nun über das unverschlüsselte HTTP-Protokoll. Um HTTPS zu verwenden, aktiviere wieder die Verschlüsselung."}, "errorDialog": {"title": "@:general.error"}, "favoriteDialog": {"title": "<PERSON><PERSON>", "noFavorites": "<PERSON><PERSON> konfigu<PERSON>t.", "addFavorite": "<PERSON>eu"}, "favoriteDeleteDialog": {"title": "<PERSON>avorit l<PERSON>", "content": "<PERSON><PERSON><PERSON><PERSON> du wirklich \"{name}\" löschen?"}, "favoriteEditDialog": {"titleAdd": "<PERSON>av<PERSON>t hinzuf<PERSON>", "titleEdit": "<PERSON><PERSON><PERSON>t bearbeiten", "name": "Name", "auto": "(auto)", "ip": "IP-Addresse", "port": "Port"}, "fileInfo": {"title": "Datei-Information", "fileName": "Dateiname:", "path": "Pfad:", "size": "Größe:", "sender": "Absender:", "time": "Zeit:"}, "fileNameInput": {"title": "Dateiname eingeben", "original": "Original: {original}"}, "historyClearDialog": {"title": "<PERSON><PERSON><PERSON><PERSON>", "content": "Möchtest du wirklich den gesamten Verlauf löschen?"}, "localNetworkUnauthorized": {"title": "@:dialogs.noPermission.title", "description": "LocalSend kann nicht auf das lokale Netzwerk zugreifen. Bitte erlaube LocalSend den Zugriff auf das lokale Netzwerk in den Einstellungen.", "gotoSettings": "Einstellungen"}, "messageInput": {"title": "Nachricht eingeben", "multiline": "Mehrzeilig"}, "noFiles": {"title": "<PERSON><PERSON> ausgewählt", "content": "<PERSON>te wähle mindestens eine Datei aus."}, "noPermission": {"title": "<PERSON><PERSON>", "content": "Sie haben die erforderlichen Berechtigungen nicht gewährt. Bitte gewähren Sie sie in den Einstellungen."}, "notAvailableOnPlatform": {"title": "Nicht verfügbar", "content": "Diese Funktion ist nur verfügbar auf:"}, "qr": {"title": "QR-Code"}, "quickActions": {"title": "Quick Actions", "counter": "<PERSON><PERSON><PERSON>", "prefix": "Prefix", "padZero": "<PERSON><PERSON> <PERSON><PERSON>", "sortBeforeCount": "Vorher alphabetisch sortieren", "random": "<PERSON><PERSON><PERSON>"}, "quickSaveNotice": {"title": "@:general.quickSave", "content": "Dateianfragen werden automatisch akzeptiert. Beach<PERSON>, dass jeder im lokalen Netzwerk dir <PERSON><PERSON> senden kann."}, "sendModeHelp": {"title": "<PERSON><PERSON><PERSON>", "single": "Sende Dateien an einen Empfänger. Die Auswahl wird nach Abschluss der Dateiübertragung gelöscht.", "multiple": "Sende Dateien an mehrere Empfänger. Die Auswahl bleibt erhalten.", "link": "<PERSON><PERSON><PERSON><PERSON>, die LocalSend nicht installiert haben, können die ausgewählten Dateien herunterladen, in dem sie den Link im Browser öffnen."}}, "tray": {"@info": "Apple Guidelines are very strict about the 'close' wording.", "open": "@:general.open", "close": "LocalSend beenden"}, "web": {"waiting": "@:sendPage.waiting", "rejected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "files": "<PERSON><PERSON>", "fileName": "Dateiname", "size": "Größe"}, "assetPicker": {"@info": "Translations for the Media selection tool for Android and Iphone", "confirm": "Bestätigen", "cancel": "Abbrechen", "edit": "<PERSON><PERSON><PERSON>", "gifIndicator": "GIF", "loadFailed": "Ladevorgang ist fehlgeschlagen", "original": "Ursprung", "preview": "Vorschau", "select": "Auswählen", "emptyList": "<PERSON><PERSON>", "unSupportedAssetType": "Format ist nicht unterstützt.", "unableToAccessAll": "<PERSON>ugriff nicht möglich", "viewingLimitedAssetsTip": "Zeigen Sie nur Dateien und Alben an, auf die die App zugreifen kann.", "changeAccessibleLimitedAssets": "<PERSON><PERSON><PERSON>, um erlaubte Dateien zu aktualisieren", "accessAllTip": "Die App kann nur auf einige der Dateien auf dem Gerät zugreifen. Öffnen Sie die Systemeinstellungen und erlauben Sie der App, \nauf alle Dateien auf dem Gerät zuzugreifen", "goToSystemSettings": "G<PERSON><PERSON> zu den Systemeinstellungen", "accessLimitedAssets": "Fahre fort mit limitiertem Zugriff", "accessiblePathName": "Verfügbare Assets", "sTypeAudioLabel": "Audio", "sTypeImageLabel": "Bild", "sTypeVideoLabel": "Video", "sTypeOtherLabel": "Andere Medien", "sActionPlayHint": "Abspielen", "sActionPreviewHint": "Vorschau", "sActionSelectHint": "Auswählen", "sActionSwitchPathLabel": "Dateipfad ändern", "sActionUseCameraHint": "<PERSON><PERSON><PERSON>", "sNameDurationLabel": "<PERSON><PERSON>", "sUnitAssetCountLabel": "<PERSON><PERSON><PERSON>"}}