{"locale": "Indonesian", "appName": "LocalSend", "general": {"accept": "Terima", "accepted": "Diterima", "add": "Tambah", "advanced": "Advanced", "cancel": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "continueStr": "<PERSON><PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON>", "copiedToClipboard": "<PERSON><PERSON><PERSON> ke Papa<PERSON>", "decline": "<PERSON><PERSON>", "done": "Se<PERSON><PERSON>", "edit": "Ubah", "error": "<PERSON><PERSON><PERSON>", "example": "<PERSON><PERSON><PERSON>", "files": "File", "finished": "Se<PERSON><PERSON>", "hide": "<PERSON><PERSON><PERSON><PERSON>", "off": "<PERSON><PERSON>", "offline": "Offline", "on": "<PERSON><PERSON><PERSON>", "online": "Online", "open": "<PERSON><PERSON>", "queue": "<PERSON><PERSON><PERSON>", "quickSave": "Penyimpanan Cepat", "renamed": "Berganti nama", "reset": "<PERSON><PERSON>", "restart": "Mengulang Kembali", "settings": "<PERSON><PERSON><PERSON><PERSON>", "skipped": "Di<PERSON><PERSON>", "start": "<PERSON><PERSON>", "stop": "<PERSON><PERSON><PERSON><PERSON>", "save": "Simpan", "unchanged": "<PERSON><PERSON><PERSON> be<PERSON>", "unknown": "Tidak dikenal"}, "receiveTab": {"title": "<PERSON><PERSON><PERSON>", "infoBox": {"ip": "IP:", "port": "Port:", "alias": "Alias:"}}, "sendTab": {"title": "<PERSON><PERSON>", "selection": {"title": "<PERSON><PERSON><PERSON>", "files": "File: {files}", "size": "Ukuran: {size}"}, "picker": {"file": "File", "folder": "Folder", "media": "Media", "text": "Teks", "app": "Aplikasi"}, "shareIntentInfo": "Anda juga dapat menggunakan fitur \"<PERSON><PERSON><PERSON>\" untuk memilih file dengan lebih mudah.", "nearbyDevices": "Perangkat terdekat", "thisDevice": "Perangkat ini", "scan": "<PERSON><PERSON>", "sendMode": "<PERSON> pengiriman", "sendModes": {"single": "<PERSON><PERSON><PERSON> tunggal", "multiple": "<PERSON><PERSON><PERSON> ganda", "link": "Bagikan melalui tautan"}, "sendModeHelp": "<PERSON><PERSON><PERSON><PERSON>", "help": "Pastikan perangkat terkoneksi pada jaringan yang sama.", "placeItems": "Tempatkan item untuk dibagikan."}, "settingsTab": {"title": "<PERSON><PERSON><PERSON><PERSON>", "general": {"title": "<PERSON><PERSON>", "brightness": "<PERSON><PERSON>", "brightnessOptions": {"system": "Sistem", "dark": "<PERSON><PERSON><PERSON>", "light": "Terang"}, "color": "<PERSON><PERSON>", "colorOptions": {"system": "Sistem"}, "language": "Bahasa", "languageOptions": {"system": "Sistem"}, "saveWindowPlacement": "Keluar: <PERSON><PERSON><PERSON> p<PERSON>i j<PERSON>", "minimizeToTray": "Keluar: <PERSON><PERSON><PERSON><PERSON> ke pojok bawah", "launchAtStartup": "<PERSON><PERSON> otom<PERSON> set<PERSON> ma<PERSON>k", "launchMinimized": "<PERSON><PERSON> otomatis: <PERSON><PERSON><PERSON><PERSON> <PERSON>", "animations": "<PERSON><PERSON><PERSON>"}, "receive": {"title": "<PERSON><PERSON><PERSON>", "quickSave": "@:general.quickSave", "destination": "<PERSON><PERSON><PERSON>", "downloads": "(<PERSON><PERSON><PERSON>)", "saveToGallery": "Simpan media ke galeri", "saveToHistory": "Simpan ke riwayat"}, "network": {"title": "<PERSON><PERSON><PERSON>", "needRestart": "<PERSON><PERSON> ulang untuk menerapkan!", "server": "Server", "alias": "<PERSON><PERSON>", "deviceType": "<PERSON><PERSON><PERSON>", "deviceModel": "<PERSON> <PERSON><PERSON>", "port": "Port", "portWarning": "You might not be detected by other devices because you are using a custom port. (default: {defaultPort})", "encryption": "Encryption", "multicastGroup": "Multicast", "multicastGroupWarning": "<PERSON>a mungkin tidak terdeteksi oleh perangkat lain karena Anda menggunakan alamat multicast khusus. (default: {defaultMulticast})"}, "advancedSettings": "Pengaturan Lanju<PERSON>"}, "troubleshootPage": {"title": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>ah", "subTitle": "Aplikasi ini tidak berfungsi seperti yang diharapkan? Di sini Anda dapat menemukan solusi umum.", "solution": "Solusi:", "fixButton": "Perbaiki secara otomatis", "firewall": {"symptom": "Aplikasi ini dapat mengirim file ke perangkat lain tetapi perangkat lain tidak dapat mengirim file ke perangkat ini.", "solution": "Ini kemungkinan masalah firewall. Anda dapat memperbaikinya dengan memperbolehkan koneksi masuk (UDP dan TCP) pada port {port}.", "openFirewall": "Buka Firewall"}, "noConnection": {"symptom": "Kedua perangkat tidak dapat saling menemukan atau berbagi file.", "solution": "Masalah ada di kedua sisi? Maka Anda perlu memastikan bahwa kedua perangkat berada dalam jaringan wifi yang sama dan berbagi konfigurasi yang sama (port, alamat multicast, enkripsi). Wifi mungkin tidak memperbolehkan komunikasi antara peserta. <PERSON><PERSON> hal ini, opsi ini harus diaktifkan pada router."}}, "receiveHistoryPage": {"title": "Riwayat", "openFolder": "Buka folder", "deleteHistory": "Hapus riwayat", "empty": "Riwayat kosong.", "entryActions": {"open": "Buka file", "info": "Informasi", "deleteFromHistory": "<PERSON><PERSON> dari riwayat"}}, "apkPickerPage": {"title": "<PERSON><PERSON><PERSON><PERSON> (APK)", "excludeSystemApps": "Kecualikan aplikasi sistem", "excludeAppsWithoutLaunchIntent": "Kecualikan aplikasi yang tidak dapat diluncurkan", "apps": "{n} <PERSON><PERSON><PERSON><PERSON>"}, "selectedFilesPage": {"deleteAll": "<PERSON><PERSON> semua"}, "receivePage": {"subTitle": {"one": "ingin men<PERSON>m file.", "other": "ingin mengirim {n} file."}, "subTitleMessage": "men<PERSON>m pesan:", "subTitleLink": "mengirim link:", "canceled": "Pengirim membatalkan permintaan."}, "receiveOptionsPage": {"title": "Opsi", "destination": "@:settingsTab.receive.destination", "appDirectory": "(LocalSend folder)", "saveToGallery": "@:settingsTab.receive.saveToGallery", "saveToGalleryOff": "<PERSON><PERSON><PERSON><PERSON> secara otomatis karena ada direktori."}, "sendPage": {"waiting": "<PERSON><PERSON><PERSON> respon...", "rejected": "<PERSON><PERSON><PERSON> permin<PERSON>.", "busy": "Penerima sedang sibuk dengan permintaan lain."}, "progressPage": {"titleSending": "Mengirim file", "titleReceiving": "Menerima file", "savedToGallery": "Simpan ke galeri", "total": {"title": {"sending": "Total proses ({time})", "finishedError": "<PERSON><PERSON><PERSON> dengan kes<PERSON>han", "canceledSender": "Dibatalkan o<PERSON>h <PERSON>m", "canceledReceiver": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>h pen<PERSON>"}, "count": "File: {curr} / {n}", "size": "Ukuran: {curr} / {n}", "speed": "Kecepatan: {speed}/s"}}, "webSharePage": {"title": "Bagikan melalui tautan", "loading": "<PERSON><PERSON><PERSON> server...", "stopping": "Menghentikan server...", "error": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han saat memulai server.", "openLink": {"one": "<PERSON><PERSON> tautan ini di browser:", "other": "<PERSON>uka salah satu tautan ini di browser:"}, "requests": "<PERSON><PERSON><PERSON><PERSON>", "noRequests": "Belum ada perminta<PERSON>.", "encryption": "@:settingsTab.network.encryption", "encryptionHint": "LocalSend menggunakan sertifikat self-signed. Anda perlu menerimanya di peramban.", "pendingRequests": "Permintaan tertunda: {n}"}, "aboutPage": {"title": "Tentang LocalSend"}, "changelogPage": {"title": "Catatan Perubahan"}, "aliasGenerator(ignoreMissing)": {"@info": "Lokasi yang berbeda mungkin memiliki kata yang berbeda, memungkinkan ketidakcocokan 1:1", "adjectives": ["<PERSON><PERSON><PERSON>", "Cantik", "Besar", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Cerdik", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Licik", "<PERSON><PERSON><PERSON>", "Giat", "E<PERSON><PERSON>n", "<PERSON><PERSON>", "Cepat", "Baik", "Segar", "Bagus", "<PERSON><PERSON><PERSON>", "Sangat Bagus", "Tampan", "Panas", "Baik", "Menyenangkan", "Mistik", "<PERSON><PERSON>", "Bagus", "Sabar", "Cantik", "Ku<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Strategis", "Tangguh", "<PERSON><PERSON>", "Bijak"], "fruits": ["Apel", "Alpukat", "<PERSON><PERSON>", "Blackberry", "Blueberry", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Lemon", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Melon", "<PERSON><PERSON>", "Bawang", "Jeruk", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Rasberi", "<PERSON><PERSON><PERSON>", "Tomat"], "combination": "{fruit} {adjective}", "@combination": "In some languages, the adjective must be last."}, "dialogs": {"addFile": {"title": "Tambah dalam pilihan", "content": "Apa yang akan ditambahkan?"}, "addressInput": {"title": "<PERSON><PERSON><PERSON>", "hashtag": "Hashtag", "ip": "Alamat IP", "recentlyUsed": "Baru saja digunakan: "}, "cancelSession": {"title": "Batalkan transfer file", "content": "<PERSON><PERSON><PERSON><PERSON> Anda yakin untuk membatalkan pengiriman file?"}, "cannotOpenFile": {"title": "Tidak dapat membuka file", "content": "Tidak dapat membuka \"{file}\". Apakah file ini telah dipindahkan, diganti nama, atau dihapus?"}, "encryptionDisabledNotice": {"title": "Enkripsi dinonaktifkan", "content": "Komunikasi sekarang terjadi melalui protokol HTTP yang tidak terenkripsi. Untuk menggunakan HTTPS, aktifkan kembali enkripsi."}, "errorDialog": {"title": "@:general.error"}, "fileInfo": {"title": "Informasi file", "fileName": "Nama file:", "path": "Path:", "size": "Ukuran:", "sender": "Pengirim:", "time": "Waktu:"}, "fileNameInput": {"title": "<PERSON><PERSON><PERSON> nama file", "original": "Asli: {original}"}, "localNetworkUnauthorized": {"title": "@:dialogs.noPermission.title", "description": "LocalSend tidak dapat menemukan perangkat lain tanpa izin untuk memindai jaringan lokal. Mohon berikan izin ini di pengaturan.", "gotoSettings": "<PERSON><PERSON><PERSON><PERSON>"}, "messageInput": {"title": "<PERSON><PERSON><PERSON> pesan", "multiline": "Multiline"}, "noFiles": {"title": "Tidak ada file yang dipilih", "content": "<PERSON><PERSON><PERSON> set<PERSON>nya satu file."}, "noPermission": {"title": "Tidak ada izin", "content": "<PERSON>a belum member<PERSON>n izin yang diperlukan. <PERSON><PERSON> berikan izin tersebut di pengaturan."}, "notAvailableOnPlatform": {"title": "Tidak tersedia", "content": "Fitur ini hanya tersedia di:"}, "qr": {"title": "Kode QR"}, "quickActions": {"title": "Tindakan Cepat", "counter": "Counter", "prefix": "<PERSON><PERSON><PERSON>", "padZero": "Pad dengan nol", "sortBeforeCount": "<PERSON>rutkan berdasarkan abjad se<PERSON>", "random": "Acak"}, "quickSaveNotice": {"title": "@:general.quickSave", "content": "<PERSON><PERSON><PERSON> permintaan akan langsung diterima. <PERSON><PERSON> berhati-hati bahwa semua orang di jaringan lokal dapat mengirim file."}, "sendModeHelp": {"title": "<PERSON> pengiriman", "single": "Mengirim file ke satu penerima. <PERSON><PERSON><PERSON> akan dihapus setelah selesai mentransfer file.", "multiple": "Mengirim file ke beberapa penerima. Pilihan tidak akan dihapus.", "link": "Penerima yang tidak memiliki LocalSend dapat mengunduh file yang dipilih dengan membuka tautan di browser mereka."}}, "tray": {"@info": "Pedoman Apple sangat ketat tentang kata-kata 'tutup'.", "open": "@:general.open", "close": "Tutup LocalSend"}, "web": {"waiting": "@:sendPage.waiting", "rejected": "<PERSON><PERSON><PERSON>", "files": "Berkas", "fileName": "Nama file", "size": "Ukuran"}, "assetPicker": {"@info": "Translations for the Media selection tool for Android and Iphone", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "edit": "Edit", "gifIndicator": "GIF", "loadFailed": "<PERSON><PERSON> memuat", "original": "<PERSON><PERSON>", "preview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON>", "emptyList": "Daftar kosong", "unSupportedAssetType": "Jenis file tidak didukung.", "unableToAccessAll": "Tidak dapat mengakses semua file di perangkat", "viewingLimitedAssetsTip": "<PERSON>ya melihat file dan album yang dapat diakses oleh aplikasi.", "changeAccessibleLimitedAssets": "Klik untuk memperbarui file yang dapat diakses", "accessAllTip": "Aplikasi hanya dapat mengakses beberapa file di perangkat. Pergi ke pengaturan sistem dan izinkan aplikasi untuk mengakses semua media di perangkat.", "goToSystemSettings": "Ke pengaturan sistem", "accessLimitedAssets": "Lanjutkan dengan akses terbatas", "accessiblePathName": "File yang dapat diakses", "sTypeAudioLabel": "Audio", "sTypeImageLabel": "Gambar", "sTypeVideoLabel": "Video", "sTypeOtherLabel": "Media lainnya", "sActionPlayHint": "mainkan", "sActionPreviewHint": "<PERSON>rat<PERSON><PERSON><PERSON>", "sActionSelectHint": "pilih", "sActionSwitchPathLabel": "ubah path", "sActionUseCameraHint": "gunakan kamera", "sNameDurationLabel": "durasi", "sUnitAssetCountLabel": "jumlah"}}