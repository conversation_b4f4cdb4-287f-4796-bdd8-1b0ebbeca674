{"locale": "English", "appName": "LocalSend", "general": {"accept": "Accept", "accepted": "Accepted", "add": "Add", "advanced": "Advanced", "cancel": "Cancel", "close": "Close", "confirm": "Confirm", "continueStr": "Continue", "copy": "Copy", "copiedToClipboard": "Copied to Clipboard", "decline": "Decline", "done": "Done", "delete": "Delete", "edit": "Edit", "error": "Error", "example": "Example", "files": "Files", "finished": "Finished", "hide": "<PERSON>de", "off": "Off", "offline": "Offline", "on": "On", "online": "Online", "open": "Open", "queue": "Queue", "quickSave": "Quick Save", "renamed": "<PERSON>amed", "reset": "Reset", "restart": "<PERSON><PERSON>", "settings": "Settings", "skipped": "Skipped", "start": "Start", "stop": "Stop", "save": "Save", "unchanged": "Unchanged", "unknown": "Unknown", "noItemInClipboard": "No item in Clipboard"}, "receiveTab": {"title": "Receive", "infoBox": {"ip": "IP:", "port": "Port:", "alias": "Device name:"}}, "sendTab": {"title": "Send", "selection": {"title": "Selection", "files": "Files: {files}", "size": "Size: {size}"}, "picker": {"file": "File", "folder": "Folder", "media": "Media", "text": "Text", "app": "App", "clipboard": "Paste"}, "shareIntentInfo": "You can also use the \"Share\" feature of your mobile device to select files more easily.", "nearbyDevices": "Nearby devices", "thisDevice": "This Device", "scan": "Look for devices", "sendMode": "Send mode", "sendModes": {"single": "Single recipient", "multiple": "Multiple recipients", "link": "Share via link"}, "sendModeHelp": "Explanation", "help": "Please ensure that the desired target is also in the same wifi network.", "placeItems": "Place items to share."}, "settingsTab": {"title": "Settings", "general": {"title": "General", "brightness": "Theme", "brightnessOptions": {"system": "System", "dark": "Dark", "light": "Light"}, "color": "Color", "colorOptions": {"system": "System", "oled": "OLED"}, "language": "Language", "languageOptions": {"system": "System"}, "saveWindowPlacement": "Quit: Save window placement", "minimizeToTray": "Quit: Minimize to Tray/Menu Bar", "launchAtStartup": "Autostart after login", "launchMinimized": "Autostart: Start hidden", "animations": "Animations"}, "receive": {"title": "Receive", "quickSave": "@:general.quickSave", "destination": "Destination", "downloads": "(Downloads)", "saveToGallery": "Save media to gallery", "saveToHistory": "Save to history"}, "network": {"title": "Network", "needRestart": "Restart the server to apply the settings!", "server": "Server", "alias": "Device name", "deviceType": "Device type", "deviceModel": "Device model", "port": "Port", "portWarning": "You might not be detected by other devices because you are using a custom port. (default: {defaultPort})", "encryption": "Encryption", "multicastGroup": "Multicast", "multicastGroupWarning": "You might not be detected by other devices because you are using a custom multicast address. (default: {defaultMulticast})"}, "advancedSettings": "Advanced settings"}, "troubleshootPage": {"title": "Troubleshoot", "subTitle": "This app does not work as expected? Here you can find common solutions.", "solution": "Solution:", "fixButton": "Fix automatically", "firewall": {"symptom": "This app can send files to other devices but other devices cannot send files to this device.", "solution": "This is most likely a firewall issue. You can solve this by allowing incoming connections (UDP and TCP) on port {port}.", "openFirewall": "Open Firewall"}, "noConnection": {"symptom": "Both devices cannot discover each other nor can they share files.", "solution": "The problem exists on both sides? Then you need to make sure that both devices are in the same wifi network and share the same configuration (port, multicast address, encryption). The wifi may not allow communication between participants. In this case, this option must be enabled on the router."}}, "receiveHistoryPage": {"title": "History", "openFolder": "Open folder", "deleteHistory": "Delete history", "empty": "The history is empty.", "entryActions": {"open": "Open file", "info": "Information", "deleteFromHistory": "Delete from history"}}, "apkPickerPage": {"title": "Apps (APK)", "excludeSystemApps": "Exclude system apps", "excludeAppsWithoutLaunchIntent": "Exclude non-launchable apps", "apps": "{n} Apps"}, "selectedFilesPage": {"deleteAll": "Delete all"}, "receivePage": {"subTitle": {"one": "wants to send you a file.", "other": "wants to send you {n} files."}, "subTitleMessage": "sent you a message:", "subTitleLink": "sent you a link:", "canceled": "The sender has canceled the request."}, "receiveOptionsPage": {"title": "Options", "destination": "@:settingsTab.receive.destination", "appDirectory": "(LocalSend folder)", "saveToGallery": "@:settingsTab.receive.saveToGallery", "saveToGalleryOff": "Turned off automatically because there are directories."}, "sendPage": {"waiting": "Waiting for response...", "rejected": "The recipient has rejected the request.", "busy": "The recipient is busy with another request."}, "progressPage": {"titleSending": "Sending files", "titleReceiving": "Receiving files", "savedToGallery": "Saved in Photos", "total": {"title": {"sending": "Total progress ({time})", "finishedError": "Finished with error", "canceledSender": "Canceled by sender", "canceledReceiver": "Canceled by receiver"}, "count": "Files: {curr} / {n}", "size": "Size: {curr} / {n}", "speed": "Speed: {speed}/s"}}, "webSharePage": {"title": "Share via link", "loading": "Starting server...", "stopping": "Stopping server...", "error": "An error occurred while starting the server.", "openLink": {"one": "Open this link in the browser:", "other": "Open one of these links in the browser:"}, "requests": "Requests", "noRequests": "No requests yet.", "encryption": "@:settingsTab.network.encryption", "encryptionHint": "LocalSend uses a self-signed certificate. You need to accept it in the browser.", "pendingRequests": "Pending requests: {n}"}, "aboutPage": {"title": "About LocalSend"}, "changelogPage": {"title": "Changelog"}, "aliasGenerator(ignoreMissing, ignoreGpt)": {"@info": "Different locales may have different words, it may not match 1:1", "adjectives": ["Adorable", "Beautiful", "Big", "<PERSON>", "Clean", "<PERSON><PERSON><PERSON>", "Cool", "Cute", "<PERSON><PERSON><PERSON>", "Determined", "Energetic", "Efficient", "Fantastic", "Fast", "Fine", "Fresh", "Good", "Gorgeous", "Great", "Handsome", "Hot", "Kind", "Lovely", "Mystic", "Neat", "Nice", "Patient", "Pretty", "Powerful", "<PERSON>", "Secret", "Smart", "Solid", "Special", "Strategic", "Strong", "Tidy", "<PERSON>"], "fruits": ["Apple", "Avocado", "Banana", "Blackberry", "Blueberry", "<PERSON><PERSON><PERSON><PERSON>", "Carrot", "Cherry", "Coconut", "Grape", "Lemon", "Lettuce", "Mango", "Melon", "Mushroom", "Onion", "Orange", "<PERSON><PERSON>", "Peach", "Pear", "Pineapple", "Potato", "<PERSON><PERSON><PERSON>", "Ra<PERSON><PERSON>", "<PERSON><PERSON>berry", "Tomato"], "combination": "{adjective} {fruit}", "@combination": "In some languages, the adjective must be last."}, "dialogs": {"addFile": {"title": "Add to selection", "content": "What do you want to add?"}, "addressInput": {"title": "Enter address", "hashtag": "Hashtag", "ip": "IP Address", "recentlyUsed": "Recently used: "}, "cancelSession": {"title": "Cancel file transfer", "content": "Do you really want to cancel the file transfer?"}, "cannotOpenFile": {"title": "Cannot open file", "content": "Could not open \"{file}\". Has this file been moved, renamed or deleted?"}, "encryptionDisabledNotice": {"title": "Encryption disabled", "content": "Communication now takes place via the unencrypted HTTP protocol. To use HTTPS, enable encryption again."}, "errorDialog": {"title": "@:general.error"}, "favoriteDialog": {"title": "Favorites", "noFavorites": "No favorites devices yet.", "addFavorite": "Add"}, "favoriteDeleteDialog": {"title": "Delete from favorites", "content": "Do you really want to delete from favorites \"{name}\"?"}, "favoriteEditDialog": {"titleAdd": "Add to favorites", "titleEdit": "Settings", "name": "Device name", "auto": "(auto)", "ip": "IP Address", "port": "Port"}, "fileInfo": {"title": "File information", "fileName": "File name:", "path": "Path:", "size": "Size:", "sender": "Sender:", "time": "Time:"}, "fileNameInput": {"title": "Enter file name", "original": "Original: {original}"}, "historyClearDialog": {"title": "Clear history", "content": "Do you really want to delete the entire history?"}, "localNetworkUnauthorized": {"title": "@:dialogs.noPermission.title", "description": "LocalSend can't find other devices without having the permission to scan the local network. Please grant this permission in the settings.", "gotoSettings": "Settings"}, "messageInput": {"title": "Type message", "multiline": "Multiline"}, "noFiles": {"title": "No file selected", "content": "Please select at least one file."}, "noPermission": {"title": "No permission", "content": "You have not granted the necessary permissions. Please grant them in the settings."}, "notAvailableOnPlatform": {"title": "Not available", "content": "This feature is only available on:"}, "qr": {"title": "QR Code"}, "quickActions": {"title": "Quick Actions", "counter": "Counter", "prefix": "Prefix", "padZero": "Pad with zeros", "sortBeforeCount": "Sort alphabetically beforehand", "random": "Random"}, "quickSaveNotice": {"title": "@:general.quickSave", "content": "File requests are automatically accepted. Be aware that everyone in the local network can send you files."}, "sendModeHelp": {"title": "Send modes", "single": "Sends files to one recipient. Selection will be cleared after finished file transfer.", "multiple": "Sends files to multiple recipients. Selection will not be cleared.", "link": "Recipients who do not have LocalSend installed can download the selected files by opening the link in their browser."}}, "tray": {"@info": "Apple Guidelines are very strict about the 'close' wording.", "open": "@:general.open", "close": "Quit LocalSend"}, "web": {"waiting": "@:sendPage.waiting", "rejected": "Rejected", "files": "Files", "fileName": "File name", "size": "Size"}, "assetPicker": {"@info": "Translations for the Media selection tool for Android and Iphone", "confirm": "Confirm", "cancel": "Cancel", "edit": "Edit", "gifIndicator": "GIF", "loadFailed": "Load failed", "original": "Origin", "preview": "Preview", "select": "Select", "emptyList": "Empty list", "unSupportedAssetType": "Unsupported file type.", "unableToAccessAll": "Unable to access all files on the device", "viewingLimitedAssetsTip": "Only view files and albums accessible to the app.", "changeAccessibleLimitedAssets": "Click to update accessible files", "accessAllTip": "<PERSON><PERSON> can only access some files on the device. Go to system settings and allow the app to access all media on the device.", "goToSystemSettings": "Go to system settings", "accessLimitedAssets": "Continue with limited access", "accessiblePathName": "Accessible files", "sTypeAudioLabel": "Audio", "sTypeImageLabel": "Image", "sTypeVideoLabel": "Video", "sTypeOtherLabel": "Other media", "sActionPlayHint": "play", "sActionPreviewHint": "preview", "sActionSelectHint": "select", "sActionSwitchPathLabel": "change path", "sActionUseCameraHint": "use camera", "sNameDurationLabel": "duration", "sUnitAssetCountLabel": "count"}}