PODS:
  - connectivity_plus (0.0.1):
    - FlutterMacOS
    - ReachabilitySwift
  - desktop_drop (0.0.1):
    - FlutterMacOS
  - device_info_plus (0.0.1):
    - FlutterMacOS
  - dynamic_color (0.0.2):
    - FlutterMacOS
  - file_selector_macos (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - gal (1.0.0):
    - Flutter
    - FlutterMacOS
  - network_info_plus (0.0.1):
    - FlutterMacOS
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - pasteboard (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - photo_manager (2.0.0):
    - Flutter
    - FlutterMacOS
  - ReachabilitySwift (5.0.0)
  - screen_retriever (0.0.1):
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - system_tray (0.0.1):
    - FlutterMacOS
  - tray_manager (0.0.1):
    - FlutterMacOS
  - url_launcher_macos (0.0.1):
    - FlutterMacOS
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - FlutterMacOS
  - window_manager (0.2.0):
    - FlutterMacOS

DEPENDENCIES:
  - connectivity_plus (from `Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos`)
  - desktop_drop (from `Flutter/ephemeral/.symlinks/plugins/desktop_drop/macos`)
  - device_info_plus (from `Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos`)
  - dynamic_color (from `Flutter/ephemeral/.symlinks/plugins/dynamic_color/macos`)
  - file_selector_macos (from `Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - gal (from `Flutter/ephemeral/.symlinks/plugins/gal/darwin`)
  - network_info_plus (from `Flutter/ephemeral/.symlinks/plugins/network_info_plus/macos`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - pasteboard (from `Flutter/ephemeral/.symlinks/plugins/pasteboard/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - photo_manager (from `Flutter/ephemeral/.symlinks/plugins/photo_manager/macos`)
  - screen_retriever (from `Flutter/ephemeral/.symlinks/plugins/screen_retriever/macos`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - system_tray (from `Flutter/ephemeral/.symlinks/plugins/system_tray/macos`)
  - tray_manager (from `Flutter/ephemeral/.symlinks/plugins/tray_manager/macos`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)
  - video_player_avfoundation (from `Flutter/ephemeral/.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos`)
  - window_manager (from `Flutter/ephemeral/.symlinks/plugins/window_manager/macos`)

SPEC REPOS:
  trunk:
    - ReachabilitySwift

EXTERNAL SOURCES:
  connectivity_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos
  desktop_drop:
    :path: Flutter/ephemeral/.symlinks/plugins/desktop_drop/macos
  device_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos
  dynamic_color:
    :path: Flutter/ephemeral/.symlinks/plugins/dynamic_color/macos
  file_selector_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  gal:
    :path: Flutter/ephemeral/.symlinks/plugins/gal/darwin
  network_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/network_info_plus/macos
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  pasteboard:
    :path: Flutter/ephemeral/.symlinks/plugins/pasteboard/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  photo_manager:
    :path: Flutter/ephemeral/.symlinks/plugins/photo_manager/macos
  screen_retriever:
    :path: Flutter/ephemeral/.symlinks/plugins/screen_retriever/macos
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  system_tray:
    :path: Flutter/ephemeral/.symlinks/plugins/system_tray/macos
  tray_manager:
    :path: Flutter/ephemeral/.symlinks/plugins/tray_manager/macos
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos
  video_player_avfoundation:
    :path: Flutter/ephemeral/.symlinks/plugins/video_player_avfoundation/darwin
  wakelock_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos
  window_manager:
    :path: Flutter/ephemeral/.symlinks/plugins/window_manager/macos

SPEC CHECKSUMS:
  connectivity_plus: 18d3c32514c886e046de60e9c13895109866c747
  desktop_drop: 69eeff437544aa619c8db7f4481b3a65f7696898
  device_info_plus: 5401765fde0b8d062a2f8eb65510fb17e77cf07f
  dynamic_color: 2eaa27267de1ca20d879fbd6e01259773fb1670f
  file_selector_macos: 468fb6b81fac7c0e88d71317f3eec34c3b008ff9
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  gal: 61e868295d28fe67ffa297fae6dacebf56fd53e1
  network_info_plus: f4fbc7877ab7b3294500d9441dfa53cd54972d05
  package_info_plus: 02d7a575e80f194102bef286361c6c326e4c29ce
  pasteboard: 9b69dba6fedbb04866be632205d532fe2f6b1d99
  path_provider_foundation: 29f094ae23ebbca9d3d0cec13889cd9060c0e943
  photo_manager: 4f6810b7dfc4feb03b461ac1a70dacf91fba7604
  ReachabilitySwift: 985039c6f7b23a1da463388634119492ff86c825
  screen_retriever: 59634572a57080243dd1bf715e55b6c54f241a38
  shared_preferences_foundation: 5b919d13b803cadd15ed2dc053125c68730e5126
  system_tray: e53c972838c69589ff2e77d6d3abfd71332f9e5d
  tray_manager: 9064e219c56d75c476e46b9a21182087930baf90
  url_launcher_macos: d2691c7dd33ed713bf3544850a623080ec693d95
  video_player_avfoundation: 8563f13d8fc8b2c29dc2d09e60b660e4e8128837
  wakelock_plus: 4783562c9a43d209c458cb9b30692134af456269
  window_manager: 3a1844359a6295ab1e47659b1a777e36773cd6e8

PODFILE CHECKSUM: 52ce3598ba3a9d91a6b91c248849f83bab8bc598

COCOAPODS: 1.13.0
