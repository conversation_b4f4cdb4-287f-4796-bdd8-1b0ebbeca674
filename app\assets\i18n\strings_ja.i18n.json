{"locale": "日本語", "appName": "LocalSend", "general": {"accept": "承諾", "accepted": "承諾済み", "add": "追加", "advanced": "詳細", "cancel": "キャンセル", "close": "閉じる", "confirm": "確認", "continueStr": "続行", "copy": "コピー", "copiedToClipboard": "クリップボードにコピーしました", "decline": "拒否", "done": "完了", "delete": "削除", "edit": "編集", "error": "エラー", "example": "例", "files": "ファイル", "finished": "完了しました", "hide": "隠す", "off": "オフ", "offline": "オフライン", "on": "オン", "online": "オンライン", "open": "開く", "queue": "順番待ち", "quickSave": "クイックセーブ", "renamed": "改名済み", "reset": "リセット", "restart": "再起動", "settings": "設定", "skipped": "スキップ済み", "start": "開始", "stop": "停止", "save": "保存", "unchanged": "未変更", "unknown": "不明", "noItemInClipboard": "クリップボードにアイテムがありません"}, "receiveTab": {"title": "受信", "infoBox": {"ip": "IP:", "port": "ポート:", "alias": "エイリアス:"}}, "sendTab": {"title": "送信", "selection": {"title": "選択", "files": "ファイル数: {files}", "size": "サイズ: {size}"}, "picker": {"file": "ファイル", "folder": "フォルダー", "media": "メディア", "text": "テキスト", "app": "アプリ", "clipboard": "ペースト"}, "shareIntentInfo": "モバイルデバイスの「共有」機能を使うと、より簡単にファイルを選択できます。", "nearbyDevices": "近くのデバイス", "thisDevice": "このデバイス", "scan": "デバイスを検索", "sendMode": "送信モード", "sendModes": {"single": "一対一送信", "multiple": "一対多送信", "link": "リンク経由で共有"}, "sendModeHelp": "説明", "help": "目標のデバイスが同じWi-Fiネットワーク内にあることを確認してください。", "placeItems": "ドロップして共有します。"}, "settingsTab": {"title": "設定", "general": {"title": "一般", "brightness": "<PERSON>るさ", "brightnessOptions": {"system": "システム", "dark": "ダーク", "light": "ライト"}, "color": "カラー", "colorOptions": {"system": "システム", "oled": "OLED"}, "language": "言語", "languageOptions": {"system": "システム"}, "saveWindowPlacement": "終了時: ウィンドウ配置を記憶", "minimizeToTray": "終了時: トレイに最小化", "launchAtStartup": "ログイン時に自動起動", "launchMinimized": "自動起動時: 隠れた状態で開始", "animations": "アニメーション"}, "receive": {"title": "受信", "quickSave": "@:general.quickSave", "destination": "保存先", "downloads": "(ダウンロード)", "saveToGallery": "メディアをギャラリーに保存", "saveToHistory": "履歴に保存"}, "network": {"title": "ネットワーク", "needRestart": "設定を反映するにはサーバーを再起動してください！", "server": "サーバー", "alias": "エイリアス", "deviceType": "デバイスタイプ", "deviceModel": "デバイスモデル", "port": "ポート", "portWarning": "カスタムポートを使用すると、このデバイスが他のデバイスから検出されない場合があります。(デフォルト: {defaultPort})", "encryption": "暗号化", "multicastGroup": "マルチキャスト", "multicastGroupWarning": "カスタムのマルチキャストアドレスを使用しているため、他のデバイスから検出されない場合があります。(デフォルト: {defaultMulticast})"}, "advancedSettings": "詳細設定"}, "troubleshootPage": {"title": "トラブルシューティング", "subTitle": "アプリが期待通りに動作しませんか？ここでは一般的な解決策を紹介します。", "solution": "解決策:", "fixButton": "自動で修正する", "firewall": {"symptom": "このアプリから他のデバイスへはファイルを送信できるのに、他のデバイスからこのデバイスへはファイルが送信できない。", "solution": "ファイアウォールに問題があると思われます。ポート{port}の受け入れ(UDPとTCP)を許可することでこの問題を解決できます。", "openFirewall": "ファイアウォールを開く"}, "noConnection": {"symptom": "どちらのデバイスも互いに表示されず、ファイルも共有できない。", "solution": "双方で問題が発生している場合は、両方のデバイスが同じWi-Fiネットワーク上にあり、同じ設定(ポート、マルチキャストアドレス、暗号化)を共有していることを確認してください。Wi-Fiが接続者同士の通信を許可していない可能性もあります。その場合は、ルーターの設定でそれを許可する必要があります。"}}, "receiveHistoryPage": {"title": "履歴", "openFolder": "フォルダを開く", "deleteHistory": "履歴を消去", "empty": "履歴には何もありません。", "entryActions": {"open": "ファイルを開く", "info": "情報", "deleteFromHistory": "履歴から削除"}}, "apkPickerPage": {"title": "アプリ (APK)", "excludeSystemApps": "システムアプリを除外", "excludeAppsWithoutLaunchIntent": "起動できないアプリを除外", "apps": "{n} 個"}, "selectedFilesPage": {"deleteAll": "すべて削除"}, "receivePage": {"subTitle": {"one": "がファイルを送信しようとしています。", "other": "が{n}件のファイルを送信しようとしています。"}, "subTitleMessage": "がメッセージを送信しました:", "subTitleLink": "がリンクを送信しました:", "canceled": "送信者がリクエストをキャンセルしました。"}, "receiveOptionsPage": {"title": "オプション", "destination": "@:settingsTab.receive.destination", "appDirectory": "(LocalSend フォルダー)", "saveToGallery": "@:settingsTab.receive.saveToGallery", "saveToGalleryOff": "ディレクトリーがあるため自動で無効になっています。"}, "sendPage": {"waiting": "返答を待っています...", "rejected": "受信者がリクエストを拒否しました。", "busy": "受信者は他のリクエストでビジー状態です。"}, "progressPage": {"titleSending": "ファイルを送信中", "titleReceiving": "ファイルを受信中", "savedToGallery": "写真に保存しました", "total": {"title": {"sending": "総進捗 ({time})", "finishedError": "エラーで終了しました", "canceledSender": "送信者によりキャンセルされました", "canceledReceiver": "受信者よりキャンセルされました"}, "count": "ファイル: {curr} / {n}", "size": "サイズ: {curr} / {n}", "speed": "速度: {speed}/s"}}, "webSharePage": {"title": "リンク経由で共有", "loading": "サーバーを起動中...", "stopping": "サーバーを停止中...", "error": "サーバーの起動中にエラーが発生しました。", "openLink": {"one": "このリンクをブラウザーで開いてください:", "other": "これらのリンクのいずれかをブラウザーで開いてください:"}, "requests": "リクエスト", "noRequests": "リクエストはまだありません。", "encryption": "@:settingsTab.network.encryption", "encryptionHint": "LocalSendは自己署名証明書を使用しています。ブラウザでそれを受け入れる必要があります。", "pendingRequests": "保留中のリクエスト数: {n}"}, "aboutPage": {"title": "LocalSendについて"}, "changelogPage": {"title": "更新履歴"}, "aliasGenerator(ignoreMissing)": {"@info": "Inherits from the English version"}, "dialogs": {"addFile": {"title": "選択に追加", "content": "何を追加しますか？"}, "addressInput": {"title": "アドレスを入力", "hashtag": "ハッシュタグ", "ip": "IPアドレス", "recentlyUsed": "最近使用したアドレス: "}, "cancelSession": {"title": "ファイル転送をキャンセル", "content": "本当にファイル転送をキャンセルしますか？"}, "cannotOpenFile": {"title": "ファイルを開けません", "content": "\"{file}\"を開けませんでした。ファイルが移動、改名、削除された可能性があります。"}, "encryptionDisabledNotice": {"title": "暗号化は無効です", "content": "これより、通信は暗号化されていないHTTPプロトコルで行われます。HTTPSを使用するには、暗号化を再度有効にしてください。"}, "errorDialog": {"title": "@:general.error"}, "favoriteDialog": {"title": "お気に入り", "noFavorites": "お気に入りした機器がありません。", "addFavorite": "追加"}, "favoriteDeleteDialog": {"title": "お気に入りから削除", "content": "本当に \"{name}\" お気に入りから削除しますか？"}, "favoriteEditDialog": {"titleAdd": "お気に入りに追加", "titleEdit": "編集", "name": "エイリアス", "auto": "(自動)", "ip": "IPアドレス", "port": "ポート"}, "fileInfo": {"title": "ファイルの情報", "fileName": "ファイル名:", "path": "ファイルパス:", "size": "サイズ:", "sender": "送信者:", "time": "時間:"}, "fileNameInput": {"title": "ファイル名を入力", "original": "元の名前: {original}"}, "historyClearDialog": {"title": "履歴をクリア", "content": "本当にすべての履歴を削除しますか？"}, "localNetworkUnauthorized": {"title": "@:dialogs.noPermission.title", "description": "ローカルネットワークをスキャンする権限がないと、LocalSendは他のデバイスを見つけることができません。設定でこの権限を付与してください。", "gotoSettings": "設定"}, "messageInput": {"title": "メッセージを入力", "multiline": "複数行"}, "noFiles": {"title": "ファイルが選択されていません", "content": "少なくとも1つのファイルを選択してください。"}, "noPermission": {"title": "権限がありません", "content": "必要な権限を許可していません。設定で許可してください。"}, "notAvailableOnPlatform": {"title": "利用不可", "content": "この機能は次のプラットフォームでのみ利用できます:"}, "qr": {"title": "QRコード"}, "quickActions": {"title": "クイックアクション", "counter": "カウンター", "prefix": "接頭辞", "padZero": "ゼロで埋める", "sortBeforeCount": "事前にアルファベット順で並べる", "random": "ランダム"}, "quickSaveNotice": {"title": "@:general.quickSave", "content": "ファイルリクエストが自動で承諾されます。ローカルネットワーク内の全員がファイルを送信できるので注意してください。"}, "sendModeHelp": {"title": "送信モード", "single": "単一の受信者にファイルを送信します。ファイルの転送完了後、選択は解除されます。", "multiple": "複数の受信者にファイルを送信します。ファイルの選択は解除されません。", "link": "LocalSendをインストールしていない受信者でも、ブラウザでリンクを開くことで選択したファイルをダウンロードできます。"}}, "tray": {"@info": "Apple Guidelines are very strict about the 'close' wording.", "open": "@:general.open", "close": "LocalSendを終了"}, "web": {"waiting": "@:sendPage.waiting", "rejected": "拒否済み", "files": "ファイル", "fileName": "ファイル名", "size": "サイズ"}, "assetPicker": {"@info": "Translations for the Media selection tool for Android and Iphone", "confirm": "確認", "cancel": "キャンセル", "edit": "編集", "gifIndicator": "GIF画像", "loadFailed": "読み込みに失敗しました", "original": "元の画像", "preview": "プレビュー", "select": "選択", "emptyList": "リストが空です", "unSupportedAssetType": "未対応のフォーマットです。", "unableToAccessAll": "デバイス内のすべてのファイルにアクセスすることができません", "viewingLimitedAssetsTip": "アプリからアクセスできるファイルやアルバムのみを表示します。", "changeAccessibleLimitedAssets": "クリックしてアクセスできるファイルを設定する", "accessAllTip": "アプリが端末の一部のファイルにしかアクセスできません。システム設定を開き、アプリがデバイス上のすべてのメディアにアクセスすることを許可してください。", "goToSystemSettings": "システム設定に移動", "accessLimitedAssets": "限られたアクセスで続ける", "accessiblePathName": "アクセス可能なファイル", "sTypeAudioLabel": "音声", "sTypeImageLabel": "画像", "sTypeVideoLabel": "動画", "sTypeOtherLabel": "その他のメディア", "sActionPlayHint": "再生", "sActionPreviewHint": "プレビュー", "sActionSelectHint": "選択", "sActionSwitchPathLabel": "パスを切り替え", "sActionUseCameraHint": "カメラを使う", "sNameDurationLabel": "長さ", "sUnitAssetCountLabel": "数"}}