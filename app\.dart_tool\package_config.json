{"configVersion": 2, "packages": [{"name": "_fe_analyzer_shared", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/_fe_analyzer_shared-72.0.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "_macros", "rootUri": "file:///C:/Users/<USER>/fvm/versions/3.24.5/bin/cache/dart-sdk/pkg/_macros", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "analyzer", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/analyzer-6.7.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "ansicolor", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/ansicolor-2.0.3", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "archive", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/archive-4.0.7", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "args", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/args-2.7.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "async", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/async-2.11.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "basic_utils", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/basic_utils-5.8.2", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "boolean_selector", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/boolean_selector-2.1.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "build", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build-2.4.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "build_config", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build_config-1.1.1", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "build_daemon", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build_daemon-4.0.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "build_resolvers", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build_resolvers-2.4.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "build_runner", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build_runner-2.4.13", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "build_runner_core", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build_runner_core-7.3.2", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "built_collection", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/built_collection-5.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "built_value", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/built_value-8.11.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "characters", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/characters-1.3.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "checked_yaml", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/checked_yaml-2.0.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "cli_config", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/cli_config-0.2.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cli_util", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/cli_util-0.4.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "clock", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/clock-1.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "code_builder", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/code_builder-4.10.1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "collection", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/collection-1.18.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "color", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/color-3.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "common", "rootUri": "../../common", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "connectivity_plus", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/connectivity_plus-6.1.4", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "connectivity_plus_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/connectivity_plus_platform_interface-2.0.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "console", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/console-4.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "convert", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/convert-3.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "coverage", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/coverage-1.15.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "cross_file", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/cross_file-0.3.4+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "crypto", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/crypto-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "csslib", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/csslib-1.0.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "csv", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/csv-6.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "dart_mappable", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/dart_mappable-4.3.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "dart_mappable_builder", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/dart_mappable_builder-4.3.1+1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "dart_style", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/dart_style-2.3.7", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "dartx", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/dartx-1.2.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "dbus", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/dbus-0.7.11", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "desktop_drop", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/desktop_drop-0.6.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "device_apps", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/device_apps-2.2.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "device_info_plus", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/device_info_plus-11.3.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "device_info_plus_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/device_info_plus_platform_interface-7.0.2", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "dio", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/dio-5.8.0+1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "dio_web_adapter", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/dio_web_adapter-2.1.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "dynamic_color", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/dynamic_color-1.7.0", "packageUri": "lib/", "languageVersion": "2.16"}, {"name": "extended_image", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/extended_image-9.1.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "extended_image_library", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/extended_image_library-4.0.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "fake_async", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/fake_async-1.3.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "ffi", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/ffi-2.1.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "file", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/file-7.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file_picker", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/file_picker-10.2.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "file_selector", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/file_selector-1.0.3", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file_selector_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/file_selector_android-0.5.1+12", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "file_selector_ios", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/file_selector_ios-0.5.3+1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "file_selector_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/file_selector_linux-0.9.3+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "file_selector_macos", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/file_selector_macos-0.9.4+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "file_selector_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/file_selector_platform_interface-2.6.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file_selector_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/file_selector_web-0.9.4+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "file_selector_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/file_selector_windows-0.9.3+4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "fixnum", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/fixnum-1.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "flutter", "rootUri": "file:///C:/Users/<USER>/fvm/default/packages/flutter", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "flutter_displaymode", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/flutter_displaymode-0.6.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_gen_core", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/flutter_gen_core-5.10.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_gen_runner", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/flutter_gen_runner-5.10.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_lints", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/flutter_lints-5.0.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_localizations", "rootUri": "file:///C:/Users/<USER>/fvm/default/packages/flutter_localizations", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "flutter_markdown", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/flutter_markdown-0.7.7+1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "flutter_plugin_android_lifecycle", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/flutter_plugin_android_lifecycle-2.0.26", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_test", "rootUri": "file:///C:/Users/<USER>/fvm/default/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "flutter_web_plugins", "rootUri": "file:///C:/Users/<USER>/fvm/default/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "frontend_server_client", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/frontend_server_client-4.0.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "gal", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/gal-2.3.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "get_it", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/get_it-7.7.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "glob", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/glob-2.1.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "graphs", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/graphs-2.3.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "hashcodes", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/hashcodes-2.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "html", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/html-0.15.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "http", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/http-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "http_client_helper", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/http_client_helper-3.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "http_methods", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/http_methods-1.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "http_multi_server", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/http_multi_server-3.2.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "http_parser", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/http_parser-4.0.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "image", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/image-4.5.4", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "image_picker", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/image_picker-1.1.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "image_picker_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/image_picker_android-0.8.12+21", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "image_picker_for_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/image_picker_for_web-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_ios", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/image_picker_linux-0.2.1+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_macos", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/image_picker_macos-0.2.1+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/image_picker_platform_interface-2.10.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/image_picker_windows-0.2.1+1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "image_size_getter", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/image_size_getter-2.4.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "intl", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/intl-0.19.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "io", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/io-1.0.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "js", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/js-0.6.7", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "json_annotation", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/json_annotation-4.9.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "launch_at_startup", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/launch_at_startup-0.3.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "leak_tracker", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/leak_tracker-10.0.5", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/leak_tracker_flutter_testing-3.0.5", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_testing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/leak_tracker_testing-3.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "lints", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/lints-5.0.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "logging", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/logging-1.3.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "macros", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/macros-0.1.2-main.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "markdown", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/markdown-7.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "matcher", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/matcher-0.12.16+1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "material_color_utilities", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "menu_base", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/menu_base-0.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "meta", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/meta-1.15.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "mime", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/mime-2.0.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "msix", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/msix-3.16.9", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "nested", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/nested-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "network_info_plus", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/network_info_plus-6.1.4", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "network_info_plus_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/network_info_plus_platform_interface-2.0.2", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "nm", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/nm-0.5.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "node_preamble", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/node_preamble-2.0.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "open_filex", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/open_filex-4.7.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "package_config", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/package_config-2.2.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "package_info_plus", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "package_info_plus_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/package_info_plus_platform_interface-3.2.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "pasteboard", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/pasteboard-0.2.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "path", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path-1.9.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_parsing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_parsing-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_provider-2.1.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_provider_android-2.2.15", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "path_provider_foundation", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_provider_linux-2.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_provider_platform_interface-2.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_provider_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_provider_windows-2.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "permission_handler", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/permission_handler-11.0.1", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "permission_handler_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/permission_handler_android-11.1.0", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "permission_handler_apple", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.1.4", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "permission_handler_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/permission_handler_platform_interface-3.12.0", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "permission_handler_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/git/permission_handler_windows_noop-fc09b707ab4535a9214c87b16f09feda7e765d90/", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "petitparser", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/petitparser-6.0.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "photo_manager", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1", "packageUri": "lib/", "languageVersion": "2.13"}, {"name": "photo_manager_image_provider", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/photo_manager_image_provider-2.2.0", "packageUri": "lib/", "languageVersion": "2.13"}, {"name": "platform", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/platform-3.1.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "plugin_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/plugin_platform_interface-2.1.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "pointycastle", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/pointycastle-4.0.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "pool", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/pool-1.5.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "posix", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/posix-6.0.3", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "pretty_qr_code", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/pretty_qr_code-3.4.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "provider", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/provider-6.1.5", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "pub_semver", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/pub_semver-2.2.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "pubspec_parse", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/pubspec_parse-1.4.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "qr", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/qr-3.0.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "refena", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/refena-3.1.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "refena_flutter", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/refena_flutter-3.1.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "refena_inspector", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/refena_inspector-2.1.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "refena_inspector_client", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/refena_inspector_client-2.1.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "routerino", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/routerino-0.8.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "screen_retriever", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/screen_retriever-0.2.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "screen_retriever_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/screen_retriever_linux-0.2.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "screen_retriever_macos", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/screen_retriever_macos-0.2.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "screen_retriever_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/screen_retriever_platform_interface-0.2.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "screen_retriever_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/screen_retriever_windows-0.2.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "share_handler", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/share_handler-0.0.23", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "share_handler_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/share_handler_android-0.0.10", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "share_handler_ios", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/share_handler_ios-0.0.15", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "share_handler_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/share_handler_platform_interface-0.0.6", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "shared_preferences", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shared_preferences-2.5.3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "shared_preferences_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shared_preferences_android-2.4.7", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "shared_preferences_foundation", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shared_preferences_linux-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shared_preferences_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shared_preferences_platform_interface-2.4.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "shared_preferences_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shared_preferences_web-2.4.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shared_preferences_windows-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shared_storage", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shared_storage-0.8.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "shelf", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shelf-1.4.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "shelf_packages_handler", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shelf_packages_handler-3.0.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "shelf_router", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shelf_router-1.1.4", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "shelf_static", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shelf_static-1.1.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shelf_web_socket", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shelf_web_socket-2.0.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shortid", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shortid-0.1.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "sky_engine", "rootUri": "file:///C:/Users/<USER>/fvm/default/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "slang", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/slang-4.7.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "slang_build_runner", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/slang_build_runner-4.7.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "slang_flutter", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/slang_flutter-4.7.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "slang_gpt", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/slang_gpt-0.11.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "source_gen", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/source_gen-1.5.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "source_map_stack_trace", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/source_map_stack_trace-2.1.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "source_maps", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/source_maps-0.10.13", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "source_span", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/source_span-1.10.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "stack_trace", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/stack_trace-1.11.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "stream_channel", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/stream_channel-2.1.2", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "stream_transform", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/stream_transform-2.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "string_scanner", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/string_scanner-1.2.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "system_settings", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/system_settings-2.1.0", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "system_tray", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/system_tray-2.0.3", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "term_glyph", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/term_glyph-1.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "test", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/test-1.25.7", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "test_api", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/test_api-0.7.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "test_core", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/test_core-0.6.4", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "time", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/time-2.1.5", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "timing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/timing-1.0.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "tray_manager", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/git/tray_manager-b37f5e088e0f02c45a684ae41e9d2da2d5c596db/", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "type_plus", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/type_plus-2.1.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "typed_data", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/typed_data-1.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "universal_platform", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/universal_platform-1.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "url_launcher", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher-6.3.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher_android-6.3.14", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "url_launcher_ios", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "url_launcher_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher_linux-3.2.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_macos", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher_macos-3.2.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher_platform_interface-2.3.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "url_launcher_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher_web-2.3.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher_windows-3.1.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "uuid", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/uuid-3.0.7", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "vector_graphics_codec", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/vector_graphics_codec-1.1.13", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "vector_graphics_compiler", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/vector_graphics_compiler-1.1.16", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "vector_math", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "video_player", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/video_player-2.9.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "video_player_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/video_player_android-2.7.16", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "video_player_avfoundation", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "video_player_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/video_player_platform_interface-6.3.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "video_player_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/video_player_web-2.3.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "visibility_detector", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/visibility_detector-0.4.0+2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "vm_service", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/vm_service-14.2.5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "wakelock_plus", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/wakelock_plus-1.3.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "wakelock_plus_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/wakelock_plus_platform_interface-1.2.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "watcher", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/watcher-1.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/web-1.1.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "web_socket", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/web_socket-1.0.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "web_socket_channel", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/web_socket_channel-3.0.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "webkit_inspection_protocol", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/webkit_inspection_protocol-1.2.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "wechat_assets_picker", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/wechat_assets_picker-9.5.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "wechat_picker_library", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/wechat_picker_library-1.0.5", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "win32", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/win32-5.10.1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "win32_registry", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/win32_registry-1.1.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "window_manager", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/window_manager-0.5.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "xdg_directories", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/xdg_directories-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "xml", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/xml-6.5.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "yaml", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/yaml-3.1.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "localsend_app", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.1"}], "generated": "2025-07-24T03:48:34.452881Z", "generator": "pub", "generatorVersion": "3.5.4", "flutterRoot": "file:///C:/Users/<USER>/fvm/default", "flutterVersion": "3.24.5", "pubCache": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache"}