# appimage-builder recipe see https://appimage-builder.readthedocs.io for details
version: 1
script:
  # Workaround for error in Github Actions (https://github.com/AppImageCrafters/appimage-builder/issues/271)
  - which mksquashfs || apt install squashfs-tools

AppDir:
  path: AppDir
  app_info:
    id: org.localsend.localsend_app
    name: LocalSend
    icon: localsend
    version: latest
    exec: localsend_app
    exec_args: $@
  apt:
    arch:
      - amd64
    allow_unauthenticated: true
    sources:
      - sourceline: deb http://archive.ubuntu.com/ubuntu/ jammy main restricted
      - sourceline: deb http://archive.ubuntu.com/ubuntu/ jammy-updates main restricted
      - sourceline: deb http://archive.ubuntu.com/ubuntu/ jammy universe
      - sourceline: deb http://archive.ubuntu.com/ubuntu/ jammy-updates universe
      - sourceline: deb http://archive.ubuntu.com/ubuntu/ jammy multiverse
      - sourceline: deb http://archive.ubuntu.com/ubuntu/ jammy-updates multiverse
      - sourceline: deb http://archive.ubuntu.com/ubuntu/ jammy-backports main restricted universe multiverse
      - sourceline: deb http://security.ubuntu.com/ubuntu/ jammy-security main restricted
      - sourceline: deb http://security.ubuntu.com/ubuntu/ jammy-security universe
      - sourceline: deb http://security.ubuntu.com/ubuntu/ jammy-security multiverse
    include:
      - libayatana-appindicator3-1:amd64
      - librsvg2-common:amd64
    exclude:
      - adwaita-icon-theme:*
  runtime:
    env:
      XDG_DATA_DIRS: '/usr/local/share/:/usr/share/:${XDG_DATA_DIRS}'
  files:
    include: []
    exclude:
      - usr/share/man
      - usr/share/doc/*/README.*
      - usr/share/doc/*/changelog.*
      - usr/share/doc/*/NEWS.*
      - usr/share/doc/*/TODO.*

# Test cases do not work in Github Actions
#
#  test:
#    fedora-30:
#      image: appimagecrafters/tests-env:fedora-30
#      command: ./AppRun
#    debian-stable:
#      image: appimagecrafters/tests-env:debian-stable
#      command: ./AppRun
#    archlinux-latest:
#      image: appimagecrafters/tests-env:archlinux-latest
#      command: ./AppRun
#    centos-7:
#      image: appimagecrafters/tests-env:centos-7
#      command: ./AppRun
#    ubuntu-xenial:
#      image: appimagecrafters/tests-env:ubuntu-xenial
#      command: ./AppRun
AppImage:
  arch: x86_64
  update-information: guess
