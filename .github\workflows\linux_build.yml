name: Linux build

on:
  workflow_dispatch:

env:
  FLUTTER_VERSION: "3.13.6"
  APK_BUILD_DIR: "/tmp/build"

jobs:
  build:
    runs-on: ubuntu-20.04
    outputs:
      version: ${{ steps.get_version.outputs.version }}

    steps:
      - uses: actions/checkout@v4

      - name: Get version from pubspec.yaml
        id: get_version
        working-directory: app
        run: |
          VERSION=$(sed -n 's/^version: \([0-9]*\.[0-9]*\.[0-9]*\).*/\1/p' pubspec.yaml)
          echo "version=$VERSION" >> $GITHUB_OUTPUT

      - uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: "stable"

      - name: Dependencies
        working-directory: app
        run: flutter pub get

      - name: Build generated files
        working-directory: app
        run: flutter pub run build_runner build -d

      - name: Upload updated lib files with generated code
        uses: actions/upload-artifact@v3
        with:
          name: lib-files
          path: ./app/lib/*

  build_appimage:
    needs: build
    runs-on: ubuntu-20.04

    steps:
      - uses: actions/checkout@v4

      - name: Download generated files
        uses: actions/download-artifact@v3
        with:
          name: lib-files
          path: app/lib

      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y clang cmake libgtk-3-dev ninja-build libayatana-appindicator3-dev libfuse2

      - uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: "stable"

      - name: Dependencies
        working-directory: app
        run: flutter pub get

      - name: Compile linux
        working-directory: app
        run: flutter build linux

      - name: Copy compiled linux files
        working-directory: app
        run: |
          mkdir AppDir
          cp -r build/linux/x64/release/bundle/* AppDir/

      - name: Copy logo to AppDir
        working-directory: app
        run: |
          mkdir -p AppDir/usr/share/icons/hicolor/32x32/apps
          cp assets/img/logo-32.png AppDir/usr/share/icons/hicolor/32x32/apps/localsend.png
          mkdir -p AppDir/usr/share/icons/hicolor/128x128/apps
          cp assets/img/logo-128.png AppDir/usr/share/icons/hicolor/128x128/apps/localsend.png
          mkdir -p AppDir/usr/share/icons/hicolor/256x256/apps
          cp assets/img/logo-256.png AppDir/usr/share/icons/hicolor/256x256/apps/localsend.png

      - name: Build AppImage
        uses: AppImageCrafters/build-appimage@57c3bc6963f870ce3be103117de5b5e33ffbaeb6
        with:
          recipe: ./app/AppImageBuilder.yml

      - name: Upload AppImage file
        uses: actions/upload-artifact@v3
        with:
          name: appimage-result
          path: ./app/*.AppImage
