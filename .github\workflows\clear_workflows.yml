name: Delete old workflow runs
on:
  workflow_dispatch:
    inputs:
      days:
        description: 'Number of days.'
        required: true
        default: 30
      minimum_runs:
        description: 'The minimum runs to keep for each workflow.'
        required: true
        default: 6
      delete_workflow_pattern:
        description: 'The name or filename of the workflow. if not set then it will target all workflows.'
        required: false
      delete_workflow_by_state_pattern:
        description: 'Remove workflow by state: active, deleted, disabled_fork, disabled_inactivity, disabled_manually'
        required: true
        default: "All"
        type: choice
        options:
          - "All"
          - active
          - deleted
          - disabled_inactivity
          - disabled_manually
      delete_run_by_conclusion_pattern:
        description: 'Remove workflow by conclusion: action_required, cancelled, failure, skipped, success'
        required: true
        default: "All"
        type: choice
        options:
          - "All"
          - action_required
          - cancelled
          - failure
          - skipped
          - success
      dry_run:
        description: 'Only log actions, do not perform any delete operations.'
        required: false

jobs:
  del_runs:
    runs-on: ubuntu-latest
    steps:
      - name: Delete workflow runs
        uses: Mattraks/delete-workflow-runs@998b89248266543a3c7827da0a73cf49d0131222 # 2.0.5
        with:
          token: ${{ github.token }}
          repository: ${{ github.repository }}
          retain_days: ${{ github.event.inputs.days }}
          keep_minimum_runs: ${{ github.event.inputs.minimum_runs }}
          delete_workflow_pattern: ${{ github.event.inputs.delete_workflow_pattern }}
          delete_workflow_by_state_pattern: ${{ github.event.inputs.delete_workflow_by_state_pattern }}
          delete_run_by_conclusion_pattern: ${{ github.event.inputs.delete_run_by_conclusion_pattern }}
          dry_run: ${{ github.event.inputs.dry_run }}
