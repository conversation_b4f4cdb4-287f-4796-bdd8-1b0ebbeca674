{"locale": "Español", "appName": "LocalSend", "general": {"accept": "Aceptar", "accepted": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON>", "advanced": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "continueStr": "<PERSON><PERSON><PERSON><PERSON>", "copy": "Copiar", "copiedToClipboard": "Copiado al Portapapeles", "decline": "<PERSON><PERSON><PERSON>", "done": "<PERSON><PERSON>", "delete": "Eliminar", "edit": "<PERSON><PERSON>", "error": "Error", "example": "Ejemplo", "files": "<PERSON><PERSON><PERSON>", "finished": "Finalizado", "hide": "Ocultar", "off": "<PERSON><PERSON><PERSON>", "offline": "Desconectado", "on": "Encendido", "online": "En línea", "open": "Abrir", "queue": "Cola", "quickSave": "Guardado Rápido", "renamed": "Renombrado", "reset": "Resetear", "restart": "Reiniciar", "settings": "<PERSON><PERSON><PERSON><PERSON>", "skipped": "Omitido", "start": "Iniciar", "stop": "Detener", "save": "Guardar", "unchanged": "Sin <PERSON>", "unknown": "Desconocido", "noItemInClipboard": "No hay ningún elemento en el portapapeles"}, "receiveTab": {"title": "Recibir", "infoBox": {"ip": "IP:", "port": "Puerto:", "alias": "Alias:"}}, "sendTab": {"title": "Enviar", "selection": {"title": "Selección", "files": "Ficheros: {files}", "size": "Tamaño: {size}"}, "picker": {"file": "<PERSON><PERSON><PERSON>", "folder": "Directorio", "media": "Media", "text": "Texto", "app": "App", "clipboard": "<PERSON><PERSON><PERSON>"}, "shareIntentInfo": "También puedes usar la opción \"Compartir\" de tu dispositivo para seleccionar ficheros más fácilmente.", "nearbyDevices": "Dispositivos Cercanos", "thisDevice": "Este dispositivo", "scan": "Buscar dispositivos", "sendMode": "<PERSON><PERSON>", "sendModes": {"single": "Solo un destino", "multiple": "<PERSON><PERSON><PERSON><PERSON> destinos", "link": "Compartir enlace"}, "sendModeHelp": "Explicación", "help": "Asegúrate de que el destino elegido está en la misma red Wi-Fi.", "placeItems": "Selecciona items para compartir."}, "settingsTab": {"title": "<PERSON><PERSON><PERSON><PERSON>", "general": {"title": "General", "brightness": "Luminosidad", "brightnessOptions": {"system": "Sistema", "dark": "Oscuro", "light": "<PERSON><PERSON><PERSON>"}, "color": "Color", "colorOptions": {"system": "Sistema", "oled": "OLED"}, "language": "Idioma", "languageOptions": {"system": "Sistema"}, "saveWindowPlacement": "Al salir guardar la ubicación de la ventana", "minimizeToTray": "Cerrar: <PERSON><PERSON><PERSON>", "launchAtStartup": "Inicio automá<PERSON>", "launchMinimized": "Inicio automático: In<PERSON>ar minimizado", "animations": "Animaciones"}, "receive": {"title": "Recibir", "quickSave": "@:general.quickSave", "destination": "<PERSON><PERSON>", "downloads": "(Descargas)", "saveToGallery": "Guardar media en la galería", "saveToHistory": "Guardar en el historial"}, "network": {"title": "Red", "needRestart": "Reiniciar el servidor para aplicar los ajustes.", "server": "<PERSON><PERSON><PERSON>", "alias": "<PERSON><PERSON>", "deviceType": "Tipo de dispositivo", "deviceModel": "Modelo de dispositivo", "port": "Puerto", "portWarning": "Es posible que no seas visible para otros dispositivos porque estás utilizando un puerto personalizado. (Puerto por defecto: {defaultPort})", "encryption": "Encriptación", "multicastGroup": "Multicast", "multicastGroupWarning": "Es posible que no seas visible para otros dispositivos porque estás utilizando una dirección multicast personalizada. (Dirección multicast por defecto: {defaultMulticast})"}, "advancedSettings": "Configuración avanzada"}, "troubleshootPage": {"title": "Solucionar problemas", "subTitle": "¿La aplicación no funciona como se espera? Aquí puedes encontrar soluciones a problemas comunes.", "solution": "Solución:", "fixButton": "Corregir automáticamente", "firewall": {"symptom": "Esta aplicación puede enviar ficheros a otros dispositivos pero otros dispositivos no pueden enviar ficheros a este.", "solution": "Lo más probable es que se trate de un problema con el firewall, puedes solucionarlo permitiendo las conexiones entrantes (UDP y TCP) en el puerto {port}.", "openFirewall": "Abrir Firewall"}, "noConnection": {"symptom": "Ambos dispositivos no pueden descubrirse ni compartir ficheros.", "solution": "¿El problema existe en ambos lados? Tienes que asegurarte que los dispositivos están en la misma red wifi y comparten la misma configuración (puerto, dirección multicast, encriptación). Es posible que el wifi no permita la comunicación entre los participantes. En este caso, hay que activar esta opción en el router."}}, "receiveHistoryPage": {"title": "Historial", "openFolder": "Abrir directorio", "deleteHistory": "Borrar historial", "empty": "El historial está vacío.", "entryActions": {"open": "<PERSON><PERSON><PERSON> fi<PERSON>o", "info": "Información", "deleteFromHistory": "Borrar del historial"}}, "apkPickerPage": {"title": "Apps (APK)", "excludeSystemApps": "Excluir apps de sistema", "excludeAppsWithoutLaunchIntent": "Excluir apps no ejecutables", "apps": "{n} Apps"}, "selectedFilesPage": {"deleteAll": "Eliminar todo"}, "receivePage": {"subTitle": {"one": "quiere enviarte un fichero.", "other": "quiere enviarte {n} ficheros."}, "subTitleMessage": "te ha enviado un mensaje:", "subTitleLink": "te ha enviado un enlace:", "canceled": "El remitente ha cancelado la petición."}, "receiveOptionsPage": {"title": "Opciones", "destination": "@:settingsTab.receive.destination", "appDirectory": "(directorio @:appName)", "saveToGallery": "@:settingsTab.receive.saveToGallery", "saveToGalleryOff": "Desactivado automáticamente porque hay directorios."}, "sendPage": {"waiting": "Esperando respuesta...", "rejected": "El destino ha rechazado la petición.", "busy": "El destino está ocupado con otra petición."}, "progressPage": {"titleSending": "<PERSON><PERSON><PERSON>", "titleReceiving": "Recibiendo ficheros", "savedToGallery": "Guardado en fotos", "total": {"title": {"sending": "Progreso total ({time})", "finishedError": "Finalizado con error", "canceledSender": "Cancelado por remitente", "canceledReceiver": "Cancelado por destino"}, "count": "Ficheros: {curr} / {n}", "size": "Tamaño: {curr} / {n}", "speed": "Velocidad: {speed}/s"}}, "webSharePage": {"title": "Compartir mediante enlace", "loading": "Iniciando servidor...", "stopping": "Deteniendo servidor...", "error": "Se ha producido un error al iniciar el servidor.", "openLink": {"one": "Abre este enlace en el navegador:", "other": "Abre uno de estos enlaces en el navegador:"}, "requests": "Solicitudes", "noRequests": "Aún no hay solicitudes.", "encryption": "@:settingsTab.network.encryption", "encryptionHint": "LocalSend utiliza un certificado autofirmado. Necesitas aceptarlo en el navegador.", "pendingRequests": "Solicitudes pendientes: {n}"}, "aboutPage": {"title": "Sobre LocalSend"}, "changelogPage": {"title": "Registro de cambios"}, "aliasGenerator(ignoreMissing)": {"@info": "Uses English version"}, "dialogs": {"addFile": {"title": "Añadir a selección", "content": "¿Qué quieres añadir?"}, "addressInput": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hashtag": "Hashtag", "ip": "Dirección IP", "recentlyUsed": "Usados recientemente: "}, "cancelSession": {"title": "Cancelar transferencia de archivos", "content": "¿Realmente quieres cancelar la transferencia de archivos?"}, "cannotOpenFile": {"title": "No se puede abrir el fichero", "content": "No se ha podido abrir \"{file}\". ¿Es posible que el fichero se haya movido, renombrado o eliminado?"}, "encryptionDisabledNotice": {"title": "Encriptación deshabilitada", "content": "La comunicación ahora tiene lugar mediante un protocolo HTTP sin encriptar. Para usar HTTPS, vuelve a habilitar la encriptación."}, "errorDialog": {"title": "@:general.error"}, "favoriteDialog": {"title": "<PERSON><PERSON><PERSON><PERSON>", "noFavorites": "Aún no hay dispositivos favoritos.", "addFavorite": "Agregar"}, "favoriteDeleteDialog": {"title": "Eliminar de favoritos", "content": "¿Realmente quieres borrar de favoritos \"{name}\"?"}, "favoriteEditDialog": {"titleAdd": "Agregar a favoritos", "titleEdit": "Ajustar", "name": "<PERSON><PERSON>", "auto": "(auto)", "ip": "Dirección IP", "port": "Puerto"}, "fileInfo": {"title": "Información del fichero", "fileName": "Nombre del fichero:", "path": "Ruta:", "size": "Tamaño:", "sender": "Emisor:", "time": "Hora:"}, "fileNameInput": {"title": "Introduce el nombre", "original": "Original: {original}"}, "historyClearDialog": {"title": "Borrar historial", "content": "¿Realmente quieres borrar todo el historial?"}, "localNetworkUnauthorized": {"title": "@:dialogs.noPermission.title", "description": "LocalSend no puede encontrar otros dispositivos sin tener permiso para escanear la red local. Por favor, concede este permiso en los ajustes.", "gotoSettings": "<PERSON><PERSON><PERSON><PERSON>"}, "messageInput": {"title": "Escribe un mensaje", "multiline": "Varias l<PERSON>"}, "noFiles": {"title": "<PERSON><PERSON><PERSON> fichero seleccionado", "content": "Por favor selecciona al menos un fichero."}, "noPermission": {"title": "Sin autorización", "content": "No has otorgado los permisos necesarios. Por favor, otórgalos en la configuración."}, "notAvailableOnPlatform": {"title": "No disponible", "content": "Esta característica solo está disponible en:"}, "qr": {"title": "Código QR"}, "quickActions": {"title": "Acciones rápidas", "counter": "<PERSON><PERSON><PERSON>", "prefix": "Prefijo", "padZero": "Llenar con ceros", "sortBeforeCount": "Ordenar alfabéticamente antes", "random": "Aleat<PERSON>"}, "quickSaveNotice": {"title": "@:general.quickSave", "content": "Las solicitudes de archivos se aceptan automáticamente. Ten en cuenta que cualquiera en la red local podrá enviarte ficheros."}, "sendModeHelp": {"title": "Modos de envío", "single": "Enviar ficheros a un solo destino. La selección se borrará una vez finalizada la transferencia.", "multiple": "Enviar ficheros a múltiples destinos. La selección no se borrará.", "link": "Los destinatarios que no tengan LocalSend instalado pueden descargar los ficheros seleccionados abriendo el enlace en su navegador."}}, "tray": {"@info": "Las directrices de Apple son muy estrictas en cuanto a la expresión 'cerrar'.", "open": "@:general.open", "close": "Cerrar LocalSend"}, "web": {"waiting": "@:sendPage.waiting", "rejected": "<PERSON><PERSON><PERSON><PERSON>", "files": "<PERSON><PERSON><PERSON>", "fileName": "Nombre del fichero", "size": "<PERSON><PERSON><PERSON>"}, "assetPicker": {"@info": "Traducciones de la herramienta de selección de medios para Android e Iphone", "confirm": "Confirmar", "cancel": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON>", "gifIndicator": "GIF", "loadFailed": "<PERSON><PERSON>r de carga", "original": "Original", "preview": "Vista previa", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "emptyList": "Lista vacía", "unSupportedAssetType": "Tipo de fichero no soportado.", "unableToAccessAll": "No se puede acceder a los archivos del dispositivo.", "viewingLimitedAssetsTip": "Ver sólo los archivos y álbumes accesibles para la aplicación.", "changeAccessibleLimitedAssets": "Haga clic para actualizar los archivos accesibles", "accessAllTip": "La aplicación sólo puede acceder a algunos archivos del dispositivo. Ve a la configuración del sistema y permite que la aplicación acceda a todos los archivos multimedia del dispositivo.", "goToSystemSettings": "Ir a los ajustes del sistema", "accessLimitedAssets": "Continuar con acceso limitado", "accessiblePathName": "Ficheros accesibles", "sTypeAudioLabel": "Audio", "sTypeImageLabel": "Imagen", "sTypeVideoLabel": "Video", "sTypeOtherLabel": "Otros medios", "sActionPlayHint": "play", "sActionPreviewHint": "vista previa", "sActionSelectHint": "selecccionar", "sActionSwitchPathLabel": "cambiar ruta", "sActionUseCameraHint": "usar c<PERSON><PERSON>", "sNameDurationLabel": "duración", "sUnitAssetCountLabel": "conteo"}}