name: localsend_app
description: An open source cross-platform alternative to AirDrop
homepage: https://localsend.org/
publish_to: "none"

version: 1.12.0+38

environment:
  flutter: ">=3.13.1"
  sdk: ">=3.1.0 <4.0.0"

dependencies:
  basic_utils: ^5.8.2
  collection: ^1.17.2 # allow newer versions, so it can compile with newer Flutter versions
  common:
    path: ../common
  connectivity_plus: ^6.1.4
  dart_mappable: ^4.3.1
  desktop_drop: ^0.6.1
  device_apps: 2.2.0
  device_info_plus: ^11.3.0
  dio: ^5.8.0+1
  dynamic_color: ^1.7.0
  file_picker: ^10.2.0
  file_selector: ^1.0.3
  flutter:
    sdk: flutter
  flutter_displaymode: 0.6.0
  flutter_localizations:
    sdk: flutter
  flutter_markdown: ^0.7.7+1
  gal: ^2.3.2
  image_picker: ^1.1.2
  intl: ^0.19.0 # allow newer versions, so it can compile with newer Flutter versions
  launch_at_startup: ^0.3.1
  logging: ^1.3.0
  mime: ^2.0.0
  network_info_plus: ^6.1.4
  open_filex: ^4.7.0
  package_info_plus: ^8.3.0
  pasteboard: ^0.2.0
  path: ^1.9.0
  path_provider: ^2.1.5
  permission_handler: 11.0.1
  pretty_qr_code: ^3.4.0
  refena_flutter: ^3.1.0
  refena_inspector_client: ^2.1.0
  routerino: 0.8.0
  screen_retriever: ^0.2.0
  share_handler: ^0.0.23
  shared_preferences: ^2.5.3
  shared_storage: ^0.8.1
  shelf: 1.4.1
  shelf_router: 1.1.4
  slang: ^4.7.3
  slang_flutter: ^4.7.0
  system_settings: 2.1.0
  system_tray: 2.0.3
  tray_manager:
    # https://github.com/leanflutter/tray_manager/issues/30
    # The Linux tray manager is disabled for now
    git:
      url: https://github.com/Tienisto/tray_manager.git
      ref: b37f5e088e0f02c45a684ae41e9d2da2d5c596db
  url_launcher: ^6.3.1
  uuid: 3.0.7
  wakelock_plus: ^1.3.2
  wechat_assets_picker: ^9.5.1
  window_manager: ^0.5.1

dev_dependencies:
  build_runner: ^2.4.13
  dart_mappable_builder: ^4.3.1+1
  flutter_gen_runner: ^5.10.0
  flutter_lints: ^5.0.0
  msix: ^3.16.9
  refena_inspector: ^2.1.0
  slang_build_runner: ^4.7.0
  slang_gpt: ^0.11.0
  test: ^1.24.3

dependency_overrides:
  permission_handler_windows:
    # Remove windows support as it causes trouble with Windows 7
    # https://github.com/Baseflow/flutter-permission-handler/issues/1034
    git:
      url: https://github.com/localsend/permission_handler_windows_noop.git
      ref: fc09b707ab4535a9214c87b16f09feda7e765d90

flutter:
  uses-material-design: true

  assets:
    - assets/img/
    - assets/web/
    - assets/CHANGELOG.md

msix_config:
  display_name: LocalSend
  publisher_display_name: Tien Do Nam
  # Using third-party CA for now (see: https://github.com/localsend/localsend/issues/220)
  # publisher: CN=0A8E9755-183F-4F0B-823F-1B8C991D7B97
  identity_name: 11157TienDoNam.LocalSend
  logo_path: assets\img\logo-512.png
  languages: en, ar, bn, cs, de, es-ES, eu, fa, fr-FR, he, hu, in, it, ja, ko, ne, nl, pl, pt-BR, ru, sv, th, tr, uk, vi, zh-CN, zh-HK, zh-TW

  # https://github.com/localsend/localsend/issues/398
  os_min_version: 10.0.19041.0

  startup_task:
    task_id: localsend
    enabled: false
    parameters: autostart
