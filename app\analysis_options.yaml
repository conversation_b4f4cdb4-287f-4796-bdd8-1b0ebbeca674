# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

# The following line activates a set of recommended lints for Flutter apps,
# packages, and plugins designed to encourage good coding practices.
include: package:flutter_lints/flutter.yaml

analyzer:
  plugins:
    - custom_lint
  exclude:
    - lib/**.g.dart
    - lib/**.freezed.dart
    - submodules/**

linter:
  rules:
    use_key_in_widget_constructors: false

    prefer_single_quotes: true
    sort_pub_dependencies: true
    always_use_package_imports: true
    directives_ordering: true
    unawaited_futures: true
    discarded_futures: true
