name: CI

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  FLUTTER_VERSION: "3.13.8"

jobs:
#  format:
#    runs-on: ubuntu-latest
#
#    steps:
#      - uses: actions/checkout@v4
#      - uses: subosito/flutter-action@v2
#        with:
#          flutter-version: ${{ env.FLUTTER_VERSION }}
#          channel: "stable"
#      - name: Dependencies
#        working-directory: app
#        run: flutter pub get
#      - name: Check format
#        working-directory: app
#        run: dart format --line-length 150 --set-exit-if-changed lib test

  test:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: "stable"
      - name: Dependencies
        working-directory: app
        run: flutter pub get
      - name: Analyze
        working-directory: app
        run: flutter analyze
      - name: Test
        working-directory: app
        run: flutter test
