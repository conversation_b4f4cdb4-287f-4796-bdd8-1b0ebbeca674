name: Bug report
description: Create a report to help us improve
title: Bug report
labels: ["bug :bug:"]
body:
  - type: textarea
    id: bug_report_description
    attributes:
      label: Describe the bug
      description: A clear and concise description of what the bug is
    validations:
      required: true
  - type: textarea
    id: bug_report_reproduce
    attributes:
      label: To reproduce
      description: Steps to reproduce the behavior
      value: |
        1. Go to '...'
        2. Click on '...'
        3. <PERSON>roll down to '...'
        4. See error
    validations:
      required: true
  - type: textarea
    id: bug_report_expected_behavior
    attributes:
      label: Expected behavior
      description: A clear and concise description of what you expected to happen
    validations:
      required: true
  - type: textarea
    id: bug_report_screenshots
    attributes:
      label: Screenshots
      description: |
        If applicable, add screenshots to help explain your problem
        Tip: You can attach images by clicking this area to highlight it and then dragging files in.
  - type: textarea
    id: bug_report_desktop
    attributes:
      label: Desktop (please complete the following information)
      placeholder: |
        - OS: [e.g. iOS]
        - Version: [e.g. 1.6.2]
  - type: textarea
    id: bug_report_smartphone
    attributes:
      label: Smartphone (please complete the following information)
      placeholder: |
        - Device: [e.g. iPhone6]
        - OS: [e.g. iOS8.1]
        - Version: [e.g. 1.6.2]
  - type: textarea
    id: bug_report_additional_context
    attributes:
      label: Additional context
      description: Add any other context about the problem here
