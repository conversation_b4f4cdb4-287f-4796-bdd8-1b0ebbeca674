PODS:
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.4):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.4)
  - DKImagePickerController/PhotoGallery (4.3.4):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.4)
  - DKPhotoGallery (0.0.17):
    - DKPhotoGallery/Core (= 0.0.17)
    - DKPhotoGallery/Model (= 0.0.17)
    - DKPhotoGallery/Preview (= 0.0.17)
    - DKPhotoGallery/Resource (= 0.0.17)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.17):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.17):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.17):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.17):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - file_selector_ios (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - gal (1.0.0):
    - Flutter
    - FlutterMacOS
  - image_picker_ios (0.0.1):
    - Flutter
  - network_info_plus (0.0.1):
    - Flutter
  - open_filex (0.0.2):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - pasteboard (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.1.1):
    - Flutter
  - photo_manager (2.0.0):
    - Flutter
    - FlutterMacOS
  - ReachabilitySwift (5.0.0)
  - SDWebImage (5.18.3):
    - SDWebImage/Core (= 5.18.3)
  - SDWebImage/Core (5.18.3)
  - share_handler_ios (0.0.11):
    - Flutter
    - share_handler_ios/share_handler_ios_models (= 0.0.11)
    - share_handler_ios_models
  - share_handler_ios/share_handler_ios_models (0.0.11):
    - Flutter
    - share_handler_ios_models
  - share_handler_ios_models (0.0.9)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.4)
  - system_settings (0.0.1):
    - Flutter
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter

DEPENDENCIES:
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - file_selector_ios (from `.symlinks/plugins/file_selector_ios/ios`)
  - Flutter (from `Flutter`)
  - gal (from `.symlinks/plugins/gal/darwin`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - network_info_plus (from `.symlinks/plugins/network_info_plus/ios`)
  - open_filex (from `.symlinks/plugins/open_filex/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - pasteboard (from `.symlinks/plugins/pasteboard/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - photo_manager (from `.symlinks/plugins/photo_manager/ios`)
  - share_handler_ios (from `.symlinks/plugins/share_handler_ios/ios`)
  - share_handler_ios_models (from `.symlinks/plugins/share_handler_ios/ios/Models`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - system_settings (from `.symlinks/plugins/system_settings/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)

SPEC REPOS:
  trunk:
    - DKImagePickerController
    - DKPhotoGallery
    - ReachabilitySwift
    - SDWebImage
    - SwiftyGif

EXTERNAL SOURCES:
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  file_selector_ios:
    :path: ".symlinks/plugins/file_selector_ios/ios"
  Flutter:
    :path: Flutter
  gal:
    :path: ".symlinks/plugins/gal/darwin"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  network_info_plus:
    :path: ".symlinks/plugins/network_info_plus/ios"
  open_filex:
    :path: ".symlinks/plugins/open_filex/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  pasteboard:
    :path: ".symlinks/plugins/pasteboard/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  photo_manager:
    :path: ".symlinks/plugins/photo_manager/ios"
  share_handler_ios:
    :path: ".symlinks/plugins/share_handler_ios/ios"
  share_handler_ios_models:
    :path: ".symlinks/plugins/share_handler_ios/ios/Models"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  system_settings:
    :path: ".symlinks/plugins/system_settings/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"

SPEC CHECKSUMS:
  connectivity_plus: bf0076dd84a130856aa636df1c71ccaff908fa1d
  device_info_plus: c6fb39579d0f423935b0c9ce7ee2f44b71b9fce6
  DKImagePickerController: b512c28220a2b8ac7419f21c491fc8534b7601ac
  DKPhotoGallery: fdfad5125a9fdda9cc57df834d49df790dbb4179
  file_picker: ce3938a0df3cc1ef404671531facef740d03f920
  file_selector_ios: 8c25d700d625e1dcdd6599f2d927072f2254647b
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  gal: 61e868295d28fe67ffa297fae6dacebf56fd53e1
  image_picker_ios: 4a8aadfbb6dc30ad5141a2ce3832af9214a705b5
  network_info_plus: 6d0c3eb8367b8164fa3fb0c19875e3f59d49697f
  open_filex: 6e26e659846ec990262224a12ef1c528bb4edbe4
  package_info_plus: 115f4ad11e0698c8c1c5d8a689390df880f47e85
  pasteboard: 982969ebaa7c78af3e6cc7761e8f5e77565d9ce0
  path_provider_foundation: 29f094ae23ebbca9d3d0cec13889cd9060c0e943
  permission_handler_apple: e76247795d700c14ea09e3a2d8855d41ee80a2e6
  photo_manager: 4f6810b7dfc4feb03b461ac1a70dacf91fba7604
  ReachabilitySwift: 985039c6f7b23a1da463388634119492ff86c825
  SDWebImage: 96e0c18ef14010b7485210e92fac888587ebb958
  share_handler_ios: ae3584532280673e02aacdf77f2cdfb2c96b9211
  share_handler_ios_models: fc638c9b4330dc7f082586c92aee9dfa0b87b871
  shared_preferences_foundation: 5b919d13b803cadd15ed2dc053125c68730e5126
  SwiftyGif: 93a1cc87bf3a51916001cf8f3d63835fb64c819f
  system_settings: 8f5cdbfa72c677fc8d665b863bcc20d393d87e9d
  url_launcher_ios: 08a3dfac5fb39e8759aeb0abbd5d9480f30fc8b4
  video_player_avfoundation: 8563f13d8fc8b2c29dc2d09e60b660e4e8128837
  wakelock_plus: 8b09852c8876491e4b6d179e17dfe2a0b5f60d47

PODFILE CHECKSUM: 21810dec9c7360014fbaacf030d9237ce547b1ed

COCOAPODS: 1.13.0
