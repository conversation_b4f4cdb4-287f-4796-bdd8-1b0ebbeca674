{"locale": "한국어", "appName": "LocalSend", "general": {"accept": "수락", "accepted": "수락됨", "add": "추가", "advanced": "상세", "cancel": "취소", "close": "닫기", "confirm": "확인", "continueStr": "계속", "copy": "복사", "copiedToClipboard": "클립보드에 복사하였습니다", "decline": "거부", "done": "완료", "delete": "삭제", "edit": "편집", "error": "에러", "example": "예시", "files": "파일", "finished": "완료되었습니다", "hide": "숨기기", "off": "꺼짐", "offline": "오프라인", "on": "켜짐", "online": "온라인", "open": "열기", "queue": "대기 중", "quickSave": "빠른 저장", "renamed": "이름 변경됨", "reset": "리셋", "restart": "재시작", "settings": "설정", "skipped": "스킵됨", "start": "시작", "stop": "정지", "save": "저장", "unchanged": "변경 안됨", "unknown": "알 수 없음", "noItemInClipboard": "클립보드에 항목이 없습니다"}, "receiveTab": {"title": "수신", "infoBox": {"ip": "IP:", "port": "포트:", "alias": "별명:"}}, "sendTab": {"title": "송신", "selection": {"title": "선택", "files": "파일 수: {files}", "size": "크기: {size}"}, "picker": {"file": "파일", "folder": "폴더", "media": "미디어", "text": "텍스트", "app": "앱", "clipboard": "붙여넣기"}, "shareIntentInfo": "휴대전화의 '공유' 기능을 사용하면 보다 편리하게 파일을 선택할 수 있습니다", "nearbyDevices": "근처의 기기", "thisDevice": "이 기기", "scan": "기기를 검색하는 중", "sendMode": "전송 모드", "sendModes": {"single": "단일 수신자", "multiple": "다중 수신자", "link": "링크로 공유하기"}, "sendModeHelp": "설명", "help": "보낼 기기가 같은 Wi-Fi 네트워크에 연결되었는지 확인해주세요", "placeItems": "드롭해서 공유"}, "settingsTab": {"title": "설정", "general": {"title": "일반", "brightness": "밝기", "brightnessOptions": {"system": "시스템", "dark": "어두움", "light": "밝음"}, "color": "색상", "colorOptions": {"system": "시스템", "oled": "OLED"}, "language": "언어", "languageOptions": {"system": "시스템"}, "saveWindowPlacement": "종료: 화면 위치 저장하기", "minimizeToTray": "종료 시 시스템 트레이로 최소화", "launchAtStartup": "로그인 시 자동으로 시작", "launchMinimized": "최소화된 상태로 시작", "animations": "애니메이션"}, "receive": {"title": "수신", "quickSave": "@:general.quickSave", "destination": "저장 위치", "downloads": "(다운로드 폴더)", "saveToGallery": "미디어를 갤러리에 저장", "saveToHistory": "히스토리에 저장"}, "network": {"title": "네트워크", "needRestart": "서버를 재시작해야 변경된 설정이 반영됩니다", "server": "서버", "alias": "별명", "deviceType": "기기 유형", "deviceModel": "기기 모델", "port": "포트", "portWarning": "커스텀 포트를 사용하면 이 디바이스가 다른 장치에서 감지되지 않을 수 있습니다. (기본값: {defaultPort})", "encryption": "암호화", "multicastGroup": "멀티캐스트", "multicastGroupWarning": "사용자 지정 멀티캐스트 주소를 사용하고 있기 때문에 다른 기기에서 감지되지 않을 수 있습니다. (기본값: {defaultMulticast})"}, "advancedSettings": "고급 설정"}, "troubleshootPage": {"title": "문제해결", "subTitle": "이 앱이 예상대로 작동하지 않나요? 여기에서 일반적인 해결 방법을 찾을 수 있습니다.", "solution": "해결방법:", "fixButton": "자동으로 수정하기", "firewall": {"symptom": "이 앱은 다른 기기로 파일을 보낼 수 있지만, 다른 기기에서 이 기기로 파일을 보낼 수 없습니다.", "solution": "방화벽 설정 때문일 가능성이 높습니다. {port} 포트로 들어오는 연결(UDP 및 TCP)을 허용하여 이 문제를 해결할 수 있습니다.", "openFirewall": "방화벽 열기"}, "noConnection": {"symptom": "두 기기 모두 서로를 검색하거나 파일을 공유할 수 없습니다.", "solution": "양쪽 모두에 문제가 있나요? 두 기기가 동일한 Wi-Fi 네트워크에 연결되어 있고 동일한 구성 (포트, 멀티캐스트 주소, 암호화)를 공유하는지 확인해야 합니다. Wi-Fi가 참가자 간 통신을 허용하지 않을 수도 있습니다. 이 경우 라우터에서 해당 옵션을 활성화해야 합니다."}}, "receiveHistoryPage": {"title": "전송 기록", "openFolder": "폴더 열기", "deleteHistory": "기록 삭제", "empty": "전송 기록이 비어 있습니다.", "entryActions": {"open": "파일 열기", "info": "정보", "deleteFromHistory": "기록에서 삭제"}}, "apkPickerPage": {"title": "앱 (APK)", "excludeSystemApps": "시스템 앱 제외", "excludeAppsWithoutLaunchIntent": "실행할 수 없는 앱 제외", "apps": "{n} 개의 앱"}, "selectedFilesPage": {"deleteAll": "모두 삭제"}, "receivePage": {"subTitle": {"one": "에서 파일을 보내려고 합니다", "other": "에서 {n}개의 파일을 보내려고 합니다"}, "subTitleMessage": "에서 메시지를 보냈습니다:", "subTitleLink": "에서 링크를 보냈습니다:", "canceled": "보내는 사람이 요청을 취소했습니다"}, "receiveOptionsPage": {"title": "옵션", "destination": "@:settingsTab.receive.destination", "appDirectory": "(LocalSend 폴더)", "saveToGallery": "@:settingsTab.receive.saveToGallery", "saveToGalleryOff": "디렉토리가 있어 자동으로 꺼집니다."}, "sendPage": {"waiting": "답변을 기다리는 중…", "rejected": "받는 사람이 요청을 거부했습니다", "busy": "수신자가 다른 요청으로 바쁩니다."}, "progressPage": {"titleSending": "파일을 보내는 중", "titleReceiving": "파일을 받는 중", "savedToGallery": "갤러리에 저장했습니다.", "total": {"title": {"sending": "진행 시간 ({time})", "finishedError": "에러로 인해 종료되었습니다", "canceledSender": "보내는 사람이 취소했습니다", "canceledReceiver": "받는 사람이 취소했습니다"}, "count": "파일: {curr} / {n}", "size": "크기: {curr} / {n}", "speed": "속도: {speed}/s"}}, "webSharePage": {"title": "링크로 공유하기", "loading": "서버 시작 중...", "stopping": "서버 중지 중...", "error": "서버 시작 중 오류가 발생했습니다.", "openLink": {"one": "이 링크를 브라우저에서 열기:", "other": "이 중 하나의 링크를 브라우저에서 열기:"}, "requests": "요청", "noRequests": "아직 요청이 없습니다.", "encryption": "@:settingsTab.network.encryption", "encryptionHint": "LocalSend는 자체 서명된 인증서를 사용합니다. 브라우저에서 수락해야 합니다.", "pendingRequests": "대기중인 요청: {n}"}, "aboutPage": {"title": "LocalSend에 대해"}, "changelogPage": {"title": "업데이트 이력"}, "aliasGenerator(ignoreMissing)": {"@info": "Inherits from the English version"}, "dialogs": {"addFile": {"title": "선택 목록에 추가", "content": "무엇을 추가할까요?"}, "addressInput": {"title": "주소를 입력", "hashtag": "해시태그", "ip": "IP 주소", "recentlyUsed": "최근 사용된 주소: "}, "cancelSession": {"title": "파일 전송을 취소", "content": "정말로 파일 전송을 취소할까요?"}, "cannotOpenFile": {"title": "파일을 열 수 없음", "content": "\"{file}\"을 열 수 없습니다. 파일이 이동, 이름 변경 또는 삭제 되었는지 확인해주세요."}, "encryptionDisabledNotice": {"title": "암호화가 비활성화되었습니다", "content": "이제부터 암호화되지 않은 HTTP 프로토콜로 통신이 이루어집니다. HTTPS를 사용하려면 암호화를 다시 활성화해주세요."}, "errorDialog": {"title": "@:general.error"}, "favoriteDialog": {"title": "즐겨찾기", "noFavorites": "즐겨찾기한 기기가 없습니다.", "addFavorite": "추가"}, "favoriteDeleteDialog": {"title": "즐겨찾기에서 삭제", "content": "정말 \"{name}\" 즐겨찾기에서 삭제할까요?"}, "favoriteEditDialog": {"titleAdd": "즐겨찾기에 추가", "titleEdit": "편집", "name": "별명", "auto": "(자동)", "ip": "IP 주소", "port": "포트"}, "fileInfo": {"title": "파일 정보", "fileName": "파일 이름:", "path": "경로:", "size": "크기:", "sender": "보낸 사람:", "time": "시간:"}, "fileNameInput": {"title": "파일 이름을 입력하세요", "original": "기존 이름: {original}"}, "historyClearDialog": {"title": "기록 지우기", "content": "정말 모든 기록을 삭제하시겠습니까?"}, "localNetworkUnauthorized": {"title": "@:dialogs.noPermission.title", "description": "로컬 네트워크를 스캔할 권한이 없어 LocalSend가 다른 기기를 찾을 수 없습니다. 설정에서 권한을 부여해주세요.", "gotoSettings": "설정"}, "messageInput": {"title": "메시지를 입력하세요", "multiline": "여러줄"}, "noFiles": {"title": "파일이 선택되지 않았습니다", "content": "적어도 하나 이상의 파일을 선택해주세요"}, "noPermission": {"title": "권한 없음", "content": "필요한 권한을 허용하지 않았습니다. 설정에서 허용해주세요."}, "notAvailableOnPlatform": {"title": "사용 불가", "content": "이 기능은 다음 플랫폼에서만 사용 가능합니다:"}, "qr": {"title": "QR 코드"}, "quickActions": {"title": "퀵 액션", "counter": "카운터", "prefix": "접두어", "padZero": "0으로 채우기", "sortBeforeCount": "미리 알파벳순으로 정렬", "random": "무작위"}, "quickSaveNotice": {"title": "@:general.quickSave", "content": "파일 요청이 자동으로 수락됩니다. 로컬 네트워크의 누구나 파일을 보낼 수 있게되므로 주의해 주세요."}, "sendModeHelp": {"title": "전송 모드", "single": "파일을 한 명의 수신자에게 보냅니다. 파일 전송이 완료되면 선택이 지워집니다.", "multiple": "파일을 여러 명의 수신자에게 보냅니다. 선택이 지워지지 않습니다.", "link": "LocalSend를 설치하지 않은 수신자는 브라우저에서 링크를 열어 선택한 파일을 다운로드할 수 있습니다."}}, "tray": {"@info": "Apple Guidelines are very strict about the 'close' wording.", "open": "@:general.open", "close": "LocalSend 종료"}, "web": {"waiting": "@:sendPage.waiting", "rejected": "거부됨", "files": "파일", "fileName": "파일 이름", "size": "크기"}, "assetPicker": {"@info": "Translations for the Media selection tool for Android and Iphone", "confirm": "확인", "cancel": "취소", "edit": "편집", "gifIndicator": "GIF", "loadFailed": "로드 실패", "original": "원본", "preview": "미리보기", "select": "선택", "emptyList": "목록이 비어있음", "unSupportedAssetType": "지원하지 않는 파일 유형입니다.", "unableToAccessAll": "기기의 모든 파일에 접근할 수 없습니다.", "viewingLimitedAssetsTip": "앱에서 접근 가능한 파일과 앨범만 볼 수 있습니다.", "changeAccessibleLimitedAssets": "접근 가능한 파일을 업데이트하려면 클릭하세요.", "accessAllTip": "앱은 기기의 일부 파일에만 접근할 수 있습니다. 시스템 설정으로 이동하여 앱이 기기의 모든 미디어에 액세스할 수 있도록 허용하세요.", "goToSystemSettings": "시스템 설정으로 이동", "accessLimitedAssets": "제한된 접근으로 계속하기", "accessiblePathName": "접근 가능한 파일", "sTypeAudioLabel": "오디오", "sTypeImageLabel": "이미지", "sTypeVideoLabel": "비디오", "sTypeOtherLabel": "기타 미디어", "sActionPlayHint": "재생", "sActionPreviewHint": "미리보기", "sActionSelectHint": "선택", "sActionSwitchPathLabel": "경로 변경", "sActionUseCameraHint": "카메라 사용", "sNameDurationLabel": "길이", "sUnitAssetCountLabel": "개수"}}